# PWA Installation Testing Guide

## Overview
This guide helps you test the PWA installation functionality for KFT Fitness app.

## Prerequisites
1. Flutter web app running on HTTPS (required for PWA)
2. Modern browser (Chrome, Edge, Firefox, Safari)
3. Service worker enabled
4. Valid web manifest

## Testing Steps

### 1. Setup Local HTTPS Server
```bash
# Option 1: Using Flutter (recommended)
flutter run -d chrome --web-port=8080 --web-hostname=localhost

# Option 2: Using Python with SSL
python -m http.server 8080 --bind localhost

# Option 3: Using Node.js serve with SSL
npx serve -s build -l 8080 --ssl-cert cert.pem --ssl-key key.pem
```

### 2. Browser-Specific Testing

#### Chrome/Edge (Chromium-based)
1. Open DevTools (F12)
2. Go to Application tab → Manifest
3. Check if manifest is valid
4. Go to Application tab → Service Workers
5. Verify service worker is registered
6. Look for install prompt in address bar
7. Test install via browser menu: "Install KFT Fitness..."

#### Firefox
1. Open about:debugging
2. Go to This Firefox → Service Workers
3. Check if service worker is running
4. Test install via address bar icon

#### Safari (iOS/macOS)
1. Check if "Add to Home Screen" appears in share menu
2. Test installation on iOS device
3. Verify standalone mode works

### 3. PWA Installation Flow Testing

#### Automatic Prompt Testing
1. **First Visit**: Should show PWA install dialog after 3 seconds
2. **Dismiss**: Click "Maybe Later" - should not show again this session
3. **Don't Ask Again**: Click "Don't Ask Again" - should not show again ever
4. **Install**: Click "Install App" - should trigger browser install prompt

#### Manual Prompt Testing
1. **Homepage Banner**: Should appear at top of homepage
2. **Banner Install**: Click install button in banner
3. **Banner Dismiss**: Click X to dismiss banner

#### Installation Verification
1. **Desktop**: App should appear in applications/start menu
2. **Mobile**: App should appear on home screen
3. **Standalone Mode**: App should open without browser UI
4. **Icon**: Correct app icon should be displayed
5. **Name**: "KFT Fitness" should be the app name

### 4. Post-Installation Testing

#### App Functionality
- [ ] App loads correctly in standalone mode
- [ ] Navigation works properly
- [ ] All features function as expected
- [ ] Offline functionality works (if implemented)
- [ ] Push notifications work (if implemented)

#### Installation State
- [ ] PWA install prompts no longer appear
- [ ] App recognizes it's installed
- [ ] Service worker continues to work
- [ ] Updates work properly

### 5. Debug Common Issues

#### Install Prompt Not Showing
1. Check HTTPS requirement
2. Verify manifest.json is valid
3. Check service worker registration
4. Ensure beforeinstallprompt event is firing
5. Check browser console for errors

#### Installation Fails
1. Verify all manifest requirements
2. Check icon sizes and formats
3. Ensure start_url is accessible
4. Verify service worker scope

#### App Doesn't Work After Install
1. Check service worker caching strategy
2. Verify all resources are cached
3. Test offline functionality
4. Check for JavaScript errors

### 6. Browser Console Commands

```javascript
// Check if PWA is installable
window.addEventListener('beforeinstallprompt', (e) => {
  console.log('PWA is installable');
});

// Check if PWA is installed
if (window.matchMedia('(display-mode: standalone)').matches) {
  console.log('PWA is installed and running in standalone mode');
}

// Check service worker status
navigator.serviceWorker.getRegistrations().then(registrations => {
  console.log('Service workers:', registrations);
});

// Check manifest
fetch('/manifest.json').then(r => r.json()).then(manifest => {
  console.log('Manifest:', manifest);
});
```

### 7. Testing Checklist

#### Pre-Installation
- [ ] HTTPS is working
- [ ] Manifest is valid and accessible
- [ ] Service worker is registered
- [ ] Icons are properly sized
- [ ] App meets PWA criteria

#### Installation Process
- [ ] beforeinstallprompt event fires
- [ ] Custom install dialog appears
- [ ] Browser install prompt works
- [ ] Installation completes successfully
- [ ] App appears in device/OS

#### Post-Installation
- [ ] App launches in standalone mode
- [ ] All functionality works
- [ ] Service worker continues working
- [ ] Updates are handled properly
- [ ] Uninstallation works

### 8. Performance Testing

#### Metrics to Check
- [ ] Time to first paint
- [ ] Time to interactive
- [ ] Service worker activation time
- [ ] Cache hit ratio
- [ ] Bundle size optimization

#### Tools
- Chrome DevTools Lighthouse
- WebPageTest
- PWA Builder validation
- Manifest validator

### 9. Cross-Platform Testing

#### Desktop Browsers
- [ ] Chrome (Windows/Mac/Linux)
- [ ] Edge (Windows/Mac)
- [ ] Firefox (Windows/Mac/Linux)
- [ ] Safari (Mac)

#### Mobile Browsers
- [ ] Chrome Mobile (Android)
- [ ] Samsung Internet (Android)
- [ ] Safari (iOS)
- [ ] Edge Mobile

### 10. Troubleshooting

#### Common Error Messages
- "Site cannot be installed": Check manifest and HTTPS
- "Service worker failed": Check SW registration and scope
- "Install prompt not showing": Verify PWA criteria
- "App won't launch": Check start_url and caching

#### Debug Resources
- Chrome DevTools Application tab
- about:serviceworkers (Firefox)
- PWA Builder validation tool
- Manifest validator tools

## Success Criteria
✅ PWA install prompts appear correctly
✅ Installation process works smoothly
✅ App functions properly when installed
✅ Service worker handles caching correctly
✅ Updates work as expected
✅ Cross-browser compatibility confirmed
