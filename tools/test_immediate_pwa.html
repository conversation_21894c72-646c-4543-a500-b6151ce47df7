<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Immediate PWA Prompt</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #3D5AFE;
            text-align: center;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .error-box {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        button {
            background: #3D5AFE;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0031CA;
        }
        .status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: inline-block;
            margin: 5px;
        }
        .status.ready {
            background: #d4edda;
            color: #155724;
        }
        .status.waiting {
            background: #fff3cd;
            color: #856404;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 5px 0;
            padding: 2px 0;
        }
        .log-entry.info {
            color: #0066cc;
        }
        .log-entry.success {
            color: #28a745;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Test Immediate PWA Prompt</h1>
        
        <div class="info-box">
            <strong>Purpose:</strong> This page tests if the PWA install prompt appears immediately when the Flutter page loads.
            <br><br>
            <strong>Instructions:</strong>
            <ol>
                <li>Make sure you're running the KFT Fitness app on HTTPS</li>
                <li>Open the app in a PWA-capable browser (Chrome, Edge, Firefox)</li>
                <li>The install prompt should appear within 1 second of page load</li>
                <li>Use this page to monitor the PWA events and timing</li>
            </ol>
        </div>

        <div id="status-container">
            <h3>PWA Status</h3>
            <span id="pwa-status" class="status waiting">Checking...</span>
            <span id="install-status" class="status waiting">Not Ready</span>
            <span id="prompt-status" class="status waiting">No Prompt</span>
        </div>

        <div>
            <button onclick="simulateInstallPrompt()">Simulate Install Prompt</button>
            <button onclick="checkPWAStatus()">Check PWA Status</button>
            <button onclick="clearLog()">Clear Log</button>
            <button onclick="testManualInstall()">Test Manual Install</button>
        </div>

        <div id="log-container">
            <h3>Event Log</h3>
            <div id="log" class="log">
                <div class="log-entry info">Initializing PWA test...</div>
            </div>
        </div>

        <div class="info-box">
            <h4>Expected Behavior:</h4>
            <ul>
                <li><strong>Immediate Prompt:</strong> Install dialog should appear within 500ms-1s of page load</li>
                <li><strong>beforeinstallprompt Event:</strong> Should fire and be captured</li>
                <li><strong>Service Worker:</strong> Should be registered and active</li>
                <li><strong>Manifest:</strong> Should be valid and accessible</li>
            </ul>
        </div>
    </div>

    <script>
        let installPromptEvent = null;
        let promptShown = false;
        let startTime = Date.now();

        function log(message, type = 'info') {
            const logContainer = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const elapsed = Date.now() - startTime;
            
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${timestamp}] (+${elapsed}ms) ${message}`;
            
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStatus(elementId, text, statusClass) {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.className = `status ${statusClass}`;
        }

        function checkPWAStatus() {
            log('Checking PWA status...', 'info');
            
            // Check HTTPS
            if (location.protocol === 'https:') {
                log('✅ HTTPS: Secure connection detected', 'success');
                updateStatus('pwa-status', 'HTTPS Ready', 'ready');
            } else {
                log('❌ HTTPS: Insecure connection - PWA requires HTTPS', 'error');
                updateStatus('pwa-status', 'HTTPS Required', 'error');
                return;
            }

            // Check Service Worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(registrations => {
                    if (registrations.length > 0) {
                        log(`✅ Service Worker: ${registrations.length} registered`, 'success');
                    } else {
                        log('⚠️ Service Worker: No registrations found', 'warning');
                    }
                });
            } else {
                log('❌ Service Worker: Not supported', 'error');
            }

            // Check Manifest
            fetch('/manifest.json')
                .then(response => response.json())
                .then(manifest => {
                    log('✅ Manifest: Valid and accessible', 'success');
                    log(`App Name: ${manifest.name}`, 'info');
                    log(`Theme Color: ${manifest.theme_color}`, 'info');
                })
                .catch(error => {
                    log(`❌ Manifest: Error loading - ${error.message}`, 'error');
                });

            // Check install prompt availability
            if (installPromptEvent) {
                log('✅ Install Prompt: beforeinstallprompt event captured', 'success');
                updateStatus('install-status', 'Ready to Install', 'ready');
            } else {
                log('⚠️ Install Prompt: beforeinstallprompt event not received yet', 'warning');
                updateStatus('install-status', 'Waiting for Event', 'waiting');
            }
        }

        function simulateInstallPrompt() {
            if (installPromptEvent) {
                log('🚀 Triggering install prompt...', 'info');
                installPromptEvent.prompt();
                
                installPromptEvent.userChoice.then(choiceResult => {
                    if (choiceResult.outcome === 'accepted') {
                        log('🎉 User accepted the install prompt', 'success');
                        updateStatus('prompt-status', 'Accepted', 'ready');
                    } else {
                        log('❌ User dismissed the install prompt', 'warning');
                        updateStatus('prompt-status', 'Dismissed', 'waiting');
                    }
                });
            } else {
                log('❌ No install prompt available', 'error');
                alert('Install prompt not available. Make sure the app meets PWA criteria.');
            }
        }

        function testManualInstall() {
            log('Testing manual install methods...', 'info');
            
            // Check if already installed
            if (window.matchMedia('(display-mode: standalone)').matches) {
                log('✅ App is already installed and running in standalone mode', 'success');
                return;
            }

            // Try different install methods
            if (installPromptEvent) {
                simulateInstallPrompt();
            } else {
                log('⚠️ Automatic install not available, check browser menu for install option', 'warning');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="log-entry info">Log cleared...</div>';
            startTime = Date.now();
        }

        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            const elapsed = Date.now() - startTime;
            log(`🎯 beforeinstallprompt event received after ${elapsed}ms`, 'success');
            
            e.preventDefault(); // Prevent default browser prompt
            installPromptEvent = e;
            
            updateStatus('install-status', 'Install Ready', 'ready');
            
            // Simulate immediate prompt (like the Flutter app should do)
            if (!promptShown) {
                promptShown = true;
                log('🚀 Simulating immediate install prompt...', 'info');
                updateStatus('prompt-status', 'Showing Prompt', 'ready');
                
                // Show prompt after brief delay (simulating Flutter app behavior)
                setTimeout(() => {
                    if (confirm('Install KFT Fitness app now?\n\n(This simulates the Flutter app prompt)')) {
                        simulateInstallPrompt();
                    } else {
                        log('User declined simulated prompt', 'warning');
                        updateStatus('prompt-status', 'Declined', 'waiting');
                    }
                }, 500);
            }
        });

        // Listen for app installed event
        window.addEventListener('appinstalled', (e) => {
            log('🎉 App was installed successfully!', 'success');
            updateStatus('prompt-status', 'Installed', 'ready');
        });

        // Check display mode
        function checkDisplayMode() {
            const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
            const isFullscreen = window.matchMedia('(display-mode: fullscreen)').matches;
            
            if (isStandalone) {
                log('📱 App is running in standalone mode (installed)', 'success');
                updateStatus('prompt-status', 'Installed', 'ready');
            } else if (isFullscreen) {
                log('📱 App is running in fullscreen mode', 'info');
            } else {
                log('🌐 App is running in browser mode', 'info');
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            log('Page loaded, starting PWA tests...', 'info');
            checkDisplayMode();
            checkPWAStatus();
            
            // Set up periodic checks for the first 10 seconds
            let checks = 0;
            const maxChecks = 50; // 10 seconds
            
            const checkInterval = setInterval(() => {
                checks++;
                
                if (installPromptEvent || checks >= maxChecks) {
                    clearInterval(checkInterval);
                    if (!installPromptEvent) {
                        log('⏰ Timeout: beforeinstallprompt event not received within 10 seconds', 'warning');
                        log('This could mean: PWA already installed, criteria not met, or browser doesn\'t support it', 'info');
                    }
                    return;
                }
                
                if (checks % 10 === 0) { // Log every 2 seconds
                    log(`Still waiting for beforeinstallprompt event... (${checks * 200}ms elapsed)`, 'info');
                }
            }, 200);
        });

        // Log page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                log('Page hidden', 'info');
            } else {
                log('Page visible', 'info');
            }
        });
    </script>
</body>
</html>
