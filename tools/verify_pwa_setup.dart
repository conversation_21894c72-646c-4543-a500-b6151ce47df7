import 'dart:io';
import 'dart:convert';

/// Script to verify PWA setup is correct
void main() async {
  print('🔍 Verifying PWA Setup for KFT Fitness...\n');
  
  bool allGood = true;
  
  // Check manifest.json
  allGood &= await checkManifest();
  
  // Check icons
  allGood &= await checkIcons();
  
  // Check service files
  allGood &= await checkServiceFiles();
  
  // Check Flutter files
  allGood &= await checkFlutterFiles();
  
  print('\n' + '='*50);
  if (allGood) {
    print('✅ PWA setup verification PASSED!');
    print('🚀 Your app should show PWA install prompts.');
  } else {
    print('❌ PWA setup verification FAILED!');
    print('🔧 Please fix the issues above.');
  }
  print('='*50);
}

Future<bool> checkManifest() async {
  print('📄 Checking web/manifest.json...');
  
  final manifestFile = File('web/manifest.json');
  if (!manifestFile.existsSync()) {
    print('❌ web/manifest.json not found');
    return false;
  }
  
  try {
    final content = await manifestFile.readAsString();
    final manifest = jsonDecode(content);
    
    // Check required fields
    final requiredFields = ['name', 'short_name', 'start_url', 'display', 'icons'];
    bool manifestOk = true;
    
    for (final field in requiredFields) {
      if (!manifest.containsKey(field)) {
        print('❌ Missing required field: $field');
        manifestOk = false;
      }
    }
    
    // Check icons array
    if (manifest['icons'] is List && (manifest['icons'] as List).isNotEmpty) {
      print('✅ Icons array present with ${(manifest['icons'] as List).length} icons');
    } else {
      print('❌ Icons array missing or empty');
      manifestOk = false;
    }
    
    if (manifestOk) {
      print('✅ web/manifest.json is valid');
      print('   App name: ${manifest['name']}');
      print('   Display mode: ${manifest['display']}');
    }
    
    return manifestOk;
  } catch (e) {
    print('❌ Error reading manifest.json: $e');
    return false;
  }
}

Future<bool> checkIcons() async {
  print('\n🎨 Checking PWA icons...');
  
  final iconsDir = Directory('web/icons');
  if (!iconsDir.existsSync()) {
    print('❌ web/icons directory not found');
    return false;
  }
  
  final requiredIcons = [
    'Icon-192.png',
    'Icon-512.png',
    'Icon-maskable-192.png',
    'Icon-maskable-512.png',
  ];
  
  bool iconsOk = true;
  
  for (final iconName in requiredIcons) {
    final iconFile = File('web/icons/$iconName');
    if (iconFile.existsSync()) {
      final size = await iconFile.length();
      print('✅ $iconName (${(size / 1024).toStringAsFixed(1)} KB)');
    } else {
      print('❌ Missing icon: $iconName');
      iconsOk = false;
    }
  }
  
  return iconsOk;
}

Future<bool> checkServiceFiles() async {
  print('\n🔧 Checking PWA service files...');
  
  bool servicesOk = true;
  
  // Check PWA service
  final pwaServiceFile = File('lib/services/pwa_install_service.dart');
  if (pwaServiceFile.existsSync()) {
    print('✅ PWA install service found');
  } else {
    print('❌ PWA install service missing');
    servicesOk = false;
  }
  
  // Check PWA widgets
  final pwaWidgetFiles = [
    'lib/widgets/pwa_install_dialog.dart',
    'lib/widgets/pwa_install_prompt.dart',
    'lib/widgets/web_pwa_prompt.dart',
  ];
  
  for (final widgetFile in pwaWidgetFiles) {
    final file = File(widgetFile);
    if (file.existsSync()) {
      print('✅ ${widgetFile.split('/').last} found');
    } else {
      print('❌ ${widgetFile.split('/').last} missing');
      servicesOk = false;
    }
  }
  
  return servicesOk;
}

Future<bool> checkFlutterFiles() async {
  print('\n📱 Checking Flutter integration...');
  
  bool flutterOk = true;
  
  // Check main.dart for PWA imports
  final mainFile = File('lib/main.dart');
  if (!mainFile.existsSync()) {
    print('❌ lib/main.dart not found');
    return false;
  }
  
  final mainContent = await mainFile.readAsString();
  
  // Check for PWA service import
  if (mainContent.contains('pwa_install_service.dart')) {
    print('✅ PWA service imported in main.dart');
  } else {
    print('❌ PWA service not imported in main.dart');
    flutterOk = false;
  }
  
  // Check for PWA widget import
  if (mainContent.contains('web_pwa_prompt.dart')) {
    print('✅ PWA widget imported in main.dart');
  } else {
    print('❌ PWA widget not imported in main.dart');
    flutterOk = false;
  }
  
  // Check for service initialization
  if (mainContent.contains('PWAInstallService().initialize()')) {
    print('✅ PWA service initialization found');
  } else {
    print('❌ PWA service initialization missing');
    flutterOk = false;
  }
  
  // Check for widget usage
  if (mainContent.contains('WebPWAPrompt') || mainContent.contains('WebPWATrigger')) {
    print('✅ PWA widgets used in app');
  } else {
    print('❌ PWA widgets not used in app');
    flutterOk = false;
  }
  
  return flutterOk;
}
