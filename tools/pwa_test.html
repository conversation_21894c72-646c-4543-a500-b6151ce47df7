<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KFT Fitness PWA Test Suite</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #3D5AFE;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.pass {
            background: #d4edda;
            color: #155724;
        }
        .status.fail {
            background: #f8d7da;
            color: #721c24;
        }
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
        button {
            background: #3D5AFE;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0031CA;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .error-box {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .code {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
        }
        .results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏋️ KFT Fitness PWA Test Suite</h1>
        
        <div class="info-box">
            <strong>Instructions:</strong> This tool tests PWA functionality for KFT Fitness app. 
            Make sure you're running the app on HTTPS and have a valid service worker.
        </div>

        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>
        <button onclick="testInstallPrompt()">Test Install Prompt</button>

        <div class="test-section">
            <h3>🔧 Basic PWA Requirements</h3>
            <div id="basicTests">
                <div class="test-item">
                    <span>HTTPS Connection</span>
                    <span class="status pending" id="httpsStatus">Pending</span>
                </div>
                <div class="test-item">
                    <span>Web Manifest Present</span>
                    <span class="status pending" id="manifestStatus">Pending</span>
                </div>
                <div class="test-item">
                    <span>Service Worker Registered</span>
                    <span class="status pending" id="swStatus">Pending</span>
                </div>
                <div class="test-item">
                    <span>Icons Available</span>
                    <span class="status pending" id="iconsStatus">Pending</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📱 Installation Features</h3>
            <div id="installTests">
                <div class="test-item">
                    <span>beforeinstallprompt Event</span>
                    <span class="status pending" id="promptStatus">Pending</span>
                </div>
                <div class="test-item">
                    <span>PWA Install Service</span>
                    <span class="status pending" id="serviceStatus">Pending</span>
                </div>
                <div class="test-item">
                    <span>Display Mode Detection</span>
                    <span class="status pending" id="displayStatus">Pending</span>
                </div>
                <div class="test-item">
                    <span>Installation State</span>
                    <span class="status pending" id="installStateStatus">Pending</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎨 Manifest Validation</h3>
            <div id="manifestTests">
                <div class="test-item">
                    <span>App Name</span>
                    <span class="status pending" id="nameStatus">Pending</span>
                </div>
                <div class="test-item">
                    <span>Theme Colors</span>
                    <span class="status pending" id="colorsStatus">Pending</span>
                </div>
                <div class="test-item">
                    <span>Start URL</span>
                    <span class="status pending" id="startUrlStatus">Pending</span>
                </div>
                <div class="test-item">
                    <span>Display Mode</span>
                    <span class="status pending" id="displayModeStatus">Pending</span>
                </div>
            </div>
        </div>

        <div class="results" id="results">
            <!-- Test results will appear here -->
        </div>
    </div>

    <script>
        let testResults = {};

        function updateStatus(testId, status, message = '') {
            const element = document.getElementById(testId);
            if (element) {
                element.className = `status ${status}`;
                element.textContent = status === 'pass' ? 'Pass' : status === 'fail' ? 'Fail' : 'Pending';
                if (message) {
                    element.title = message;
                }
            }
            testResults[testId] = { status, message };
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type === 'error' ? 'error-box' : 'info-box';
            div.innerHTML = message;
            results.appendChild(div);
        }

        async function testHTTPS() {
            const isHTTPS = location.protocol === 'https:';
            updateStatus('httpsStatus', isHTTPS ? 'pass' : 'fail', 
                isHTTPS ? 'Running on HTTPS' : 'PWA requires HTTPS');
            return isHTTPS;
        }

        async function testManifest() {
            try {
                const response = await fetch('/manifest.json');
                const manifest = await response.json();
                updateStatus('manifestStatus', 'pass', 'Manifest loaded successfully');
                
                // Test manifest properties
                updateStatus('nameStatus', manifest.name ? 'pass' : 'fail', 
                    manifest.name || 'App name missing');
                updateStatus('colorsStatus', 
                    (manifest.theme_color && manifest.background_color) ? 'pass' : 'fail',
                    'Theme and background colors present');
                updateStatus('startUrlStatus', manifest.start_url ? 'pass' : 'fail',
                    manifest.start_url || 'Start URL missing');
                updateStatus('displayModeStatus', manifest.display ? 'pass' : 'fail',
                    manifest.display || 'Display mode missing');
                
                addResult(`<strong>Manifest Details:</strong><div class="code">${JSON.stringify(manifest, null, 2)}</div>`);
                return true;
            } catch (error) {
                updateStatus('manifestStatus', 'fail', error.message);
                addResult(`Manifest Error: ${error.message}`, 'error');
                return false;
            }
        }

        async function testServiceWorker() {
            if ('serviceWorker' in navigator) {
                try {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    const hasRegistration = registrations.length > 0;
                    updateStatus('swStatus', hasRegistration ? 'pass' : 'fail',
                        hasRegistration ? `${registrations.length} service worker(s) registered` : 'No service workers found');
                    
                    if (hasRegistration) {
                        addResult(`<strong>Service Workers:</strong><div class="code">${registrations.map(r => r.scope).join('<br>')}</div>`);
                    }
                    return hasRegistration;
                } catch (error) {
                    updateStatus('swStatus', 'fail', error.message);
                    return false;
                }
            } else {
                updateStatus('swStatus', 'fail', 'Service Worker not supported');
                return false;
            }
        }

        async function testIcons() {
            try {
                const response = await fetch('/manifest.json');
                const manifest = await response.json();
                const hasIcons = manifest.icons && manifest.icons.length > 0;
                updateStatus('iconsStatus', hasIcons ? 'pass' : 'fail',
                    hasIcons ? `${manifest.icons.length} icons defined` : 'No icons found');
                
                if (hasIcons) {
                    const iconSizes = manifest.icons.map(icon => icon.sizes).join(', ');
                    addResult(`<strong>Icon Sizes:</strong> ${iconSizes}`);
                }
                return hasIcons;
            } catch (error) {
                updateStatus('iconsStatus', 'fail', error.message);
                return false;
            }
        }

        function testInstallPrompt() {
            let promptReceived = false;
            
            window.addEventListener('beforeinstallprompt', (e) => {
                promptReceived = true;
                updateStatus('promptStatus', 'pass', 'beforeinstallprompt event received');
                addResult('<strong>Install Prompt:</strong> beforeinstallprompt event fired - PWA is installable!');
            });

            // Check if already received
            setTimeout(() => {
                if (!promptReceived) {
                    updateStatus('promptStatus', 'fail', 'beforeinstallprompt event not received');
                    addResult('Install prompt not available. This could mean:<br>• PWA is already installed<br>• PWA criteria not met<br>• Browser doesn\'t support installation', 'error');
                }
            }, 2000);
        }

        function testPWAService() {
            // Test if PWA install service is available
            if (typeof PWAInstallService !== 'undefined') {
                updateStatus('serviceStatus', 'pass', 'PWA Install Service available');
                addResult('<strong>PWA Service:</strong> KFT PWA Install Service is loaded and available');
            } else {
                updateStatus('serviceStatus', 'fail', 'PWA Install Service not found');
                addResult('PWA Install Service not found. Make sure the service is properly imported.', 'error');
            }
        }

        function testDisplayMode() {
            const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
            const isFullscreen = window.matchMedia('(display-mode: fullscreen)').matches;
            const isMinimalUI = window.matchMedia('(display-mode: minimal-ui)').matches;
            
            let displayMode = 'browser';
            if (isStandalone) displayMode = 'standalone';
            else if (isFullscreen) displayMode = 'fullscreen';
            else if (isMinimalUI) displayMode = 'minimal-ui';
            
            updateStatus('displayStatus', 'pass', `Display mode: ${displayMode}`);
            updateStatus('installStateStatus', isStandalone ? 'pass' : 'pending',
                isStandalone ? 'App is installed' : 'App not installed or running in browser');
            
            addResult(`<strong>Display Mode:</strong> ${displayMode}`);
        }

        async function runAllTests() {
            addResult('<strong>Starting PWA Test Suite...</strong>');
            
            // Clear previous results
            const results = document.getElementById('results');
            results.innerHTML = '';
            
            // Run tests
            await testHTTPS();
            await testManifest();
            await testServiceWorker();
            await testIcons();
            testInstallPrompt();
            testPWAService();
            testDisplayMode();
            
            // Summary
            const passCount = Object.values(testResults).filter(r => r.status === 'pass').length;
            const failCount = Object.values(testResults).filter(r => r.status === 'fail').length;
            const totalTests = Object.keys(testResults).length;
            
            addResult(`<strong>Test Summary:</strong> ${passCount}/${totalTests} tests passed, ${failCount} failed`);
            
            if (failCount === 0) {
                addResult('<strong>🎉 All tests passed!</strong> Your PWA should be ready for installation.', 'info');
            } else {
                addResult('<strong>⚠️ Some tests failed.</strong> Please fix the issues above before testing installation.', 'error');
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testResults = {};
            
            // Reset all status indicators
            const statusElements = document.querySelectorAll('.status');
            statusElements.forEach(el => {
                el.className = 'status pending';
                el.textContent = 'Pending';
                el.title = '';
            });
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });

        // Listen for install prompt
        testInstallPrompt();
    </script>
</body>
</html>
