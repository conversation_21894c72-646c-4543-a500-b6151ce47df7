# PWA Installation Troubleshooting Guide

## 🚨 PWA Not Showing Install Prompt

### Quick Checklist

1. **HTT<PERSON> Required** ✅
   - P<PERSON> only works on HTTPS (or localhost for development)
   - Check: `location.protocol === 'https:'`

2. **Valid Manifest** ✅
   - Must be accessible at `/manifest.json`
   - Must have required fields: name, icons, start_url, display
   - Check: Open `/manifest.json` in browser

3. **Service Worker** ✅
   - Must be registered and active
   - Check: DevTools → Application → Service Workers

4. **Icons Available** ✅
   - At least 192x192 and 512x512 icons required
   - Check: Icons exist in `/web/icons/` directory

## 🔧 Step-by-Step Debugging

### Step 1: Test Basic PWA Requirements

Open your browser's DevTools (F12) and run:

```javascript
// Check HTTPS
console.log('HTTPS:', location.protocol === 'https:');

// Check manifest
fetch('/manifest.json').then(r => r.json()).then(console.log);

// Check service worker
navigator.serviceWorker.getRegistrations().then(console.log);

// Check for beforeinstallprompt
window.addEventListener('beforeinstallprompt', (e) => {
  console.log('PWA is installable!', e);
});
```

### Step 2: Use PWA Test Page

1. Navigate to `/pwa_test.html` in your browser
2. This page will test all PWA requirements
3. Look for any red error messages
4. The page will automatically show install prompt if available

### Step 3: Check Flutter App Integration

1. Open browser DevTools Console
2. Look for PWA-related debug messages:
   ```
   🌐 Initializing Web PWA Prompt...
   🔧 Setting up Web PWA logic...
   ✅ PWA Service initialized successfully
   🔍 Checking PWA installability...
   ```

3. If you see errors, check the service initialization

### Step 4: Verify Manifest Content

Your `web/manifest.json` should look like this:

```json
{
    "name": "KFT Fitness - Your Personal Trainer",
    "short_name": "KFT Fitness",
    "start_url": ".",
    "display": "standalone",
    "background_color": "#121212",
    "theme_color": "#3D5AFE",
    "description": "Transform your fitness journey...",
    "icons": [
        {
            "src": "icons/Icon-192.png",
            "sizes": "192x192",
            "type": "image/png"
        },
        {
            "src": "icons/Icon-512.png",
            "sizes": "512x512",
            "type": "image/png"
        }
    ]
}
```

## 🐛 Common Issues & Solutions

### Issue 1: "beforeinstallprompt event not firing"

**Causes:**
- App already installed
- PWA criteria not met
- Browser doesn't support PWA
- Running on HTTP instead of HTTPS

**Solutions:**
1. Uninstall existing PWA and try again
2. Check all PWA requirements are met
3. Test in Chrome/Edge (best PWA support)
4. Ensure you're on HTTPS

### Issue 2: "Service Worker not registered"

**Causes:**
- Flutter service worker not generated
- Service worker file missing
- Registration failed

**Solutions:**
1. Run `flutter build web` to generate service worker
2. Check `/flutter_service_worker.js` exists
3. Clear browser cache and reload

### Issue 3: "Manifest not found or invalid"

**Causes:**
- Manifest file missing
- Invalid JSON syntax
- Missing required fields
- Icons don't exist

**Solutions:**
1. Verify `/web/manifest.json` exists
2. Validate JSON syntax
3. Check all icon files exist in `/web/icons/`
4. Ensure required fields are present

### Issue 4: "Install prompt appears but installation fails"

**Causes:**
- Invalid manifest
- Missing icons
- Service worker issues
- Browser restrictions

**Solutions:**
1. Check browser console for errors
2. Verify all icon sizes exist
3. Test service worker registration
4. Try different browser

## 🧪 Testing Commands

### Run Flutter App with PWA

```bash
# Development (localhost - PWA works)
flutter run -d chrome --web-port=8080

# Production build
flutter build web
cd build/web
python -m http.server 8080

# With HTTPS (required for PWA)
flutter run -d chrome --web-port=8080 --web-hostname=0.0.0.0
```

### Test PWA Installability

```javascript
// In browser console
let deferredPrompt;
window.addEventListener('beforeinstallprompt', (e) => {
  e.preventDefault();
  deferredPrompt = e;
  console.log('PWA installable!');
});

// Trigger install
if (deferredPrompt) {
  deferredPrompt.prompt();
}
```

## 🔍 Browser-Specific Issues

### Chrome/Edge
- Best PWA support
- Shows install button in address bar
- Check: chrome://flags/#enable-desktop-pwas

### Firefox
- Limited PWA support
- May not show automatic prompts
- Manual install via address bar

### Safari
- iOS: Add to Home Screen
- macOS: Limited PWA support
- Different installation flow

## 📱 Mobile Testing

### Android Chrome
1. Open app in Chrome
2. Look for "Add to Home Screen" in menu
3. Or automatic prompt should appear

### iOS Safari
1. Open app in Safari
2. Tap Share button
3. Select "Add to Home Screen"

## 🛠️ Debug Tools

### 1. Chrome DevTools
- Application tab → Manifest
- Application tab → Service Workers
- Console for error messages
- Lighthouse PWA audit

### 2. PWA Test Page
- Navigate to `/pwa_test.html`
- Real-time PWA status checking
- Automatic install prompt testing

### 3. Flutter Debug Console
- Look for PWA service messages
- Check service initialization
- Monitor installability changes

## ✅ Success Indicators

You'll know PWA is working when:

1. **Console shows:**
   ```
   ✅ PWA Service initialized successfully
   🎯 PWA installable detected
   🚀 Showing Web PWA install prompt
   ```

2. **Browser shows:**
   - Install button in address bar (Chrome/Edge)
   - Custom install dialog appears
   - No console errors

3. **After installation:**
   - App appears in OS app list
   - Opens in standalone mode
   - No browser UI visible

## 🆘 Still Not Working?

If PWA still doesn't work after following this guide:

1. **Check Flutter version:**
   ```bash
   flutter --version
   flutter doctor
   ```

2. **Clear everything and rebuild:**
   ```bash
   flutter clean
   flutter pub get
   flutter build web
   ```

3. **Test with minimal example:**
   - Create new Flutter project
   - Add PWA configuration
   - Test if basic PWA works

4. **Browser cache issues:**
   - Hard refresh (Ctrl+Shift+R)
   - Clear browser data
   - Try incognito mode

5. **Check network:**
   - Ensure stable internet connection
   - Test on different network
   - Check firewall/proxy settings

## 📞 Getting Help

If you're still having issues:

1. Check browser console for specific error messages
2. Test the `/pwa_test.html` page
3. Verify all files exist and are accessible
4. Test in multiple browsers
5. Check Flutter and browser versions

The PWA should work if all requirements are met and the implementation is correct!
