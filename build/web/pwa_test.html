<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KFT Fitness PWA Test</title>
    <link rel="manifest" href="manifest.json">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #3D5AFE;
            text-align: center;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #3D5AFE;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0031CA;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏋️ KFT Fitness PWA Test</h1>
        
        <div id="status-container">
            <div id="https-status" class="status">Checking HTTPS...</div>
            <div id="manifest-status" class="status">Checking Manifest...</div>
            <div id="sw-status" class="status">Checking Service Worker...</div>
            <div id="install-status" class="status">Checking Install Prompt...</div>
        </div>

        <div>
            <button onclick="testInstall()">Test Install</button>
            <button onclick="checkStatus()">Refresh Status</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div id="log" class="log">
            <div>PWA Test initialized...</div>
        </div>
    </div>

    <script>
        let installPromptEvent = null;
        let startTime = Date.now();

        function log(message) {
            const logContainer = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const elapsed = Date.now() - startTime;
            
            const entry = document.createElement('div');
            entry.textContent = `[${timestamp}] (+${elapsed}ms) ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function checkHTTPS() {
            if (location.protocol === 'https:') {
                updateStatus('https-status', '✅ HTTPS: Secure connection', 'success');
                log('HTTPS check passed');
                return true;
            } else {
                updateStatus('https-status', '❌ HTTPS: Required for PWA', 'error');
                log('HTTPS check failed - PWA requires HTTPS');
                return false;
            }
        }

        async function checkManifest() {
            try {
                const response = await fetch('/manifest.json');
                const manifest = await response.json();
                updateStatus('manifest-status', '✅ Manifest: Valid', 'success');
                log(`Manifest loaded: ${manifest.name}`);
                return true;
            } catch (error) {
                updateStatus('manifest-status', '❌ Manifest: Error loading', 'error');
                log(`Manifest error: ${error.message}`);
                return false;
            }
        }

        async function checkServiceWorker() {
            if ('serviceWorker' in navigator) {
                try {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    if (registrations.length > 0) {
                        updateStatus('sw-status', '✅ Service Worker: Registered', 'success');
                        log(`Service Worker registered: ${registrations.length} found`);
                        return true;
                    } else {
                        updateStatus('sw-status', '⚠️ Service Worker: Not registered', 'warning');
                        log('Service Worker not registered');
                        return false;
                    }
                } catch (error) {
                    updateStatus('sw-status', '❌ Service Worker: Error', 'error');
                    log(`Service Worker error: ${error.message}`);
                    return false;
                }
            } else {
                updateStatus('sw-status', '❌ Service Worker: Not supported', 'error');
                log('Service Worker not supported');
                return false;
            }
        }

        function checkInstallPrompt() {
            if (installPromptEvent) {
                updateStatus('install-status', '✅ Install: Ready', 'success');
                log('Install prompt is ready');
                return true;
            } else {
                updateStatus('install-status', '⏳ Install: Waiting for prompt', 'warning');
                log('Install prompt not ready yet');
                return false;
            }
        }

        async function checkStatus() {
            log('Running PWA status checks...');
            
            const httpsOk = checkHTTPS();
            const manifestOk = await checkManifest();
            const swOk = await checkServiceWorker();
            const installOk = checkInstallPrompt();
            
            if (httpsOk && manifestOk && swOk && installOk) {
                log('✅ All PWA requirements met!');
            } else {
                log('⚠️ Some PWA requirements not met');
            }
        }

        function testInstall() {
            if (installPromptEvent) {
                log('Triggering install prompt...');
                installPromptEvent.prompt();
                
                installPromptEvent.userChoice.then(choiceResult => {
                    if (choiceResult.outcome === 'accepted') {
                        log('🎉 User accepted install');
                        updateStatus('install-status', '✅ Install: Accepted', 'success');
                    } else {
                        log('❌ User dismissed install');
                        updateStatus('install-status', '⚠️ Install: Dismissed', 'warning');
                    }
                });
            } else {
                log('❌ No install prompt available');
                alert('Install prompt not available. Check PWA requirements.');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>Log cleared...</div>';
            startTime = Date.now();
        }

        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            const elapsed = Date.now() - startTime;
            log(`🎯 beforeinstallprompt event received after ${elapsed}ms`);
            
            e.preventDefault();
            installPromptEvent = e;
            
            updateStatus('install-status', '✅ Install: Ready', 'success');
            
            // Auto-show prompt after 1 second (simulating Flutter app)
            setTimeout(() => {
                if (confirm('Install KFT Fitness app now?\n\n(This simulates the Flutter app behavior)')) {
                    testInstall();
                }
            }, 1000);
        });

        // Listen for app installed event
        window.addEventListener('appinstalled', (e) => {
            log('🎉 App installed successfully!');
            updateStatus('install-status', '✅ Install: Installed', 'success');
        });

        // Check display mode
        function checkDisplayMode() {
            if (window.matchMedia('(display-mode: standalone)').matches) {
                log('📱 Running in standalone mode (installed)');
                updateStatus('install-status', '✅ Install: Already installed', 'success');
            } else {
                log('🌐 Running in browser mode');
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            log('Page loaded, starting PWA tests...');
            checkDisplayMode();
            checkStatus();
            
            // Register a simple service worker if none exists
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/flutter_service_worker.js')
                    .then(registration => {
                        log('Service Worker registered successfully');
                        checkServiceWorker();
                    })
                    .catch(error => {
                        log(`Service Worker registration failed: ${error.message}`);
                    });
            }
        });
    </script>
</body>
</html>
