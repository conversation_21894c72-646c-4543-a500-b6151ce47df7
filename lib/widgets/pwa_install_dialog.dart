import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/pwa_install_service.dart';
import '../design_system/kft_design_system.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// Dialog that prompts users to install the PWA
/// Features attractive design with clear benefits and call-to-action
class PWAInstallDialog extends StatefulWidget {
  final VoidCallback? onInstallSuccess;
  final VoidCallback? onDismiss;
  final bool showDontAskAgain;

  const PWAInstallDialog({
    Key? key,
    this.onInstallSuccess,
    this.onDismiss,
    this.showDontAskAgain = true,
  }) : super(key: key);

  /// Show the PWA install dialog
  static Future<void> show(
    BuildContext context, {
    VoidCallback? onInstallSuccess,
    VoidCallback? onDismiss,
    bool showDontAskAgain = true,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => PWAInstallDialog(
        onInstallSuccess: onInstallSuccess,
        onDismiss: onDismiss,
        showDontAskAgain: showDontAskAgain,
      ),
    );
  }

  @override
  State<PWAInstallDialog> createState() => _PWAInstallDialogState();
}

class _PWAInstallDialogState extends State<PWAInstallDialog>
    with TickerProviderStateMixin {
  final PWAInstallService _pwaService = PWAInstallService();
  bool _isInstalling = false;
  late AnimationController _scaleController;
  late AnimationController _slideController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _scaleController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _handleInstall() async {
    if (_isInstalling) return;

    setState(() {
      _isInstalling = true;
    });

    try {
      final success = await _pwaService.promptInstall();
      
      if (success) {
        // Show success animation briefly
        await Future.delayed(const Duration(milliseconds: 500));
        
        if (mounted) {
          Navigator.of(context).pop();
          widget.onInstallSuccess?.call();
        }
      } else {
        setState(() {
          _isInstalling = false;
        });
      }
    } catch (e) {
      debugPrint('❌ Error during PWA installation: $e');
      setState(() {
        _isInstalling = false;
      });
    }
  }

  Future<void> _handleDismiss() async {
    await _scaleController.reverse();
    if (mounted) {
      Navigator.of(context).pop();
      widget.onDismiss?.call();
    }
  }

  Future<void> _handleDontAskAgain() async {
    await _pwaService.dismissPermanently();
    await _handleDismiss();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Dialog(
      backgroundColor: Colors.transparent,
      child: SlideTransition(
        position: _slideAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusXl),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(theme, isDarkMode),
                _buildContent(theme),
                _buildActions(theme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            KFTDesignSystem.primaryColor,
            KFTDesignSystem.primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(KFTDesignSystem.borderRadiusXl),
          topRight: Radius.circular(KFTDesignSystem.borderRadiusXl),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const FaIcon(
              FontAwesomeIcons.download,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Install KFT Fitness',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Get the full app experience',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Why install the app?',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildBenefit(
            theme,
            FontAwesomeIcons.bolt,
            'Faster Performance',
            'Lightning-fast loading and smooth animations',
          ),
          const SizedBox(height: 12),
          _buildBenefit(
            theme,
            FontAwesomeIcons.wifi,
            'Works Offline',
            'Access your workouts even without internet',
          ),
          const SizedBox(height: 12),
          _buildBenefit(
            theme,
            FontAwesomeIcons.bell,
            'Push Notifications',
            'Get reminders for workouts and progress updates',
          ),
          const SizedBox(height: 12),
          _buildBenefit(
            theme,
            FontAwesomeIcons.mobile,
            'Native Experience',
            'Feels like a native app on your device',
          ),
        ],
      ),
    );
  }

  Widget _buildBenefit(ThemeData theme, IconData icon, String title, String description) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: KFTDesignSystem.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: FaIcon(
            icon,
            color: KFTDesignSystem.primaryColor,
            size: 16,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActions(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Install button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isInstalling ? null : _handleInstall,
              style: ElevatedButton.styleFrom(
                backgroundColor: KFTDesignSystem.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusLg),
                ),
                elevation: 0,
              ),
              child: _isInstalling
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const FaIcon(FontAwesomeIcons.download, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Install App',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
          const SizedBox(height: 12),
          // Action buttons row
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: _isInstalling ? null : _handleDismiss,
                  child: Text(
                    'Maybe Later',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                    ),
                  ),
                ),
              ),
              if (widget.showDontAskAgain) ...[
                const SizedBox(width: 8),
                Expanded(
                  child: TextButton(
                    onPressed: _isInstalling ? null : _handleDontAskAgain,
                    child: Text(
                      'Don\'t Ask Again',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}
