import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/pwa_install_service.dart';
import '../widgets/pwa_install_dialog.dart';
import '../design_system/kft_design_system.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// Widget that manages PWA installation prompts
/// Shows prompts at appropriate times and handles user interactions
class PWAInstallPrompt extends StatefulWidget {
  final Widget child;
  final bool showOnAppLaunch;
  final bool showOnHomePage;
  final Duration delayBeforePrompt;

  const PWAInstallPrompt({
    Key? key,
    required this.child,
    this.showOnAppLaunch = true,
    this.showOnHomePage = true,
    this.delayBeforePrompt = const Duration(seconds: 3),
  }) : super(key: key);

  @override
  State<PWAInstallPrompt> createState() => _PWAInstallPromptState();
}

class _PWAInstallPromptState extends State<PWAInstallPrompt> {
  final PWAInstallService _pwaService = PWAInstallService();
  bool _hasShownPrompt = false;
  bool _isPromptVisible = false;

  @override
  void initState() {
    super.initState();
    _setupPromptLogic();
  }

  void _setupPromptLogic() {
    if (!kIsWeb) return;

    // Listen to installability changes
    _pwaService.installabilityStream.listen((isInstallable) {
      if (isInstallable && !_hasShownPrompt && mounted) {
        _schedulePrompt();
      }
    });

    // Check if we should show prompt immediately
    if (_pwaService.shouldShowInstallPrompt() && !_hasShownPrompt) {
      _schedulePrompt();
    }
  }

  void _schedulePrompt() {
    if (_isPromptVisible || _hasShownPrompt) return;

    Future.delayed(widget.delayBeforePrompt, () {
      if (mounted && _pwaService.shouldShowInstallPrompt() && !_hasShownPrompt) {
        _showInstallPrompt();
      }
    });
  }

  Future<void> _showInstallPrompt() async {
    if (_isPromptVisible || _hasShownPrompt || !mounted) return;

    setState(() {
      _isPromptVisible = true;
      _hasShownPrompt = true;
    });

    await PWAInstallDialog.show(
      context,
      onInstallSuccess: () {
        debugPrint('🎉 PWA installed successfully!');
        setState(() {
          _isPromptVisible = false;
        });
      },
      onDismiss: () {
        debugPrint('📱 PWA install prompt dismissed');
        setState(() {
          _isPromptVisible = false;
        });
      },
    );

    if (mounted) {
      setState(() {
        _isPromptVisible = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Compact PWA install banner that can be shown at the top of pages
class PWAInstallBanner extends StatefulWidget {
  final VoidCallback? onDismiss;
  final bool showCloseButton;

  const PWAInstallBanner({
    Key? key,
    this.onDismiss,
    this.showCloseButton = true,
  }) : super(key: key);

  @override
  State<PWAInstallBanner> createState() => _PWAInstallBannerState();
}

class _PWAInstallBannerState extends State<PWAInstallBanner>
    with SingleTickerProviderStateMixin {
  final PWAInstallService _pwaService = PWAInstallService();
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  bool _isVisible = false;
  bool _isInstalling = false;

  @override
  void initState() {
    super.initState();
    _setupAnimation();
    _checkVisibility();
    _listenToInstallability();
  }

  void _setupAnimation() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _checkVisibility() {
    if (!kIsWeb) return;

    final shouldShow = _pwaService.shouldShowInstallPrompt();
    if (shouldShow != _isVisible) {
      setState(() {
        _isVisible = shouldShow;
      });

      if (_isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  void _listenToInstallability() {
    _pwaService.installabilityStream.listen((_) {
      _checkVisibility();
    });

    _pwaService.installationStream.listen((isInstalled) {
      if (isInstalled && _isVisible) {
        _dismiss();
      }
    });
  }

  Future<void> _handleInstall() async {
    if (_isInstalling) return;

    setState(() {
      _isInstalling = true;
    });

    try {
      final success = await _pwaService.promptInstall();
      if (success) {
        _dismiss();
      }
    } catch (e) {
      debugPrint('❌ Error installing PWA: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isInstalling = false;
        });
      }
    }
  }

  void _dismiss() {
    _animationController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _isVisible = false;
        });
        widget.onDismiss?.call();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible || !kIsWeb) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, -1),
        end: Offset.zero,
      ).animate(_slideAnimation),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              KFTDesignSystem.primaryColor,
              KFTDesignSystem.primaryColor.withOpacity(0.8),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const FaIcon(
                  FontAwesomeIcons.download,
                  color: Colors.white,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Install KFT Fitness App',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      'Get faster access and offline features',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              TextButton(
                onPressed: _isInstalling ? null : _handleInstall,
                style: TextButton.styleFrom(
                  backgroundColor: Colors.white.withOpacity(0.2),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isInstalling
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text('Install'),
              ),
              if (widget.showCloseButton) ...[
                const SizedBox(width: 8),
                IconButton(
                  onPressed: _dismiss,
                  icon: const Icon(Icons.close, color: Colors.white, size: 20),
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                  padding: EdgeInsets.zero,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
