import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
// Web-specific imports
import 'dart:html' as html;
import 'dart:ui' as ui;
import '../models/course_video.dart';
import '../utils/video_security_helper.dart';
import '../services/progress_service.dart';
import '../services/video_streak_service.dart';

/// Official Vimeo Embed Player - Clean, reliable implementation using Vimeo's official embed
/// Handles both web and mobile platforms with proper fallbacks
class OfficialVimeoPlayer extends StatefulWidget {
  final CourseVideo video;
  final bool autoPlay;
  final VoidCallback? onReady;
  final VoidCallback? onPlay;
  final VoidCallback? onPause;
  final VoidCallback? onCompleted;
  final Function(int position)? onProgress;
  final Function(String error)? onError;
  final VoidCallback? onEnterFullscreen;
  final VoidCallback? onExitFullscreen;

  const OfficialVimeoPlayer({
    Key? key,
    required this.video,
    this.autoPlay = true,
    this.onReady,
    this.onPlay,
    this.onPause,
    this.onCompleted,
    this.onProgress,
    this.onError,
    this.onEnterFullscreen,
    this.onExitFullscreen,
  }) : super(key: key);

  @override
  State<OfficialVimeoPlayer> createState() => _OfficialVimeoPlayerState();
}

class _OfficialVimeoPlayerState extends State<OfficialVimeoPlayer> 
    with SingleTickerProviderStateMixin {
  
  // Player state
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isPlaying = false;
  bool _isCompleted = false;
  bool _isFullscreen = false;

  // Controllers
  WebViewController? _webViewController;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  // Services
  final ProgressService _progressService = ProgressService();
  final VideoStreakService _streakService = VideoStreakService();

  // Progress tracking
  Timer? _progressTimer;
  int _lastProgressUpdate = 0;
  static const int _progressUpdateInterval = 5; // seconds

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 350),
    );
    _scaleAnimation = Tween<double>(begin: 0.97, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _animationController.value = 1.0;

    // Initialize player after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializePlayer();
    });

    // Listen for orientation changes on mobile
    if (!kIsWeb) {
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);
    }
  }

  @override
  void dispose() {
    _progressTimer?.cancel();
    _animationController.dispose();
    
    // Restore overlays and orientation for extra reliability
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    
    super.dispose();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // For web platform, use HtmlElementView
      if (kIsWeb) {
        debugPrint('OfficialVimeoPlayer: Using HtmlElementView for web platform');
        setState(() {
          _isLoading = false;
        });
        widget.onReady?.call();
        return;
      }

      // For mobile platforms, use WebView
      if (!kIsWeb && WebViewPlatform.instance == null) {
        throw Exception('WebView platform is not available on this device');
      }

      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
      if (vimeoId == null) {
        throw Exception('Invalid Vimeo video URL: ${widget.video.videoUrl}');
      }

      // Create WebViewController for mobile
      _webViewController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(NavigationDelegate(
          onPageFinished: (_) {
            Future.delayed(const Duration(seconds: 1), () {
              if (mounted) {
                setState(() {
                  _isLoading = false;
                });
                widget.onReady?.call();
                _startProgressTracking();
              }
            });
          },
          onWebResourceError: (error) {
            debugPrint('OfficialVimeoPlayer: WebView error: ${error.description}');
            if (mounted) {
              setState(() {
                _hasError = true;
                _errorMessage = 'Failed to load video: ${error.description}';
                _isLoading = false;
              });
              widget.onError?.call(error.description);
            }
          },
        ));

      // Load the Vimeo embed HTML
      final embedHtml = _buildVimeoEmbedHtml(vimeoId);
      await _webViewController!.loadHtmlString(embedHtml);

    } catch (e) {
      debugPrint('OfficialVimeoPlayer: Initialization error: $e');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = 'Failed to initialize video player: $e';
          _isLoading = false;
        });
        widget.onError?.call(e.toString());
      }
    }
  }

  String _buildVimeoEmbedHtml(String vimeoId) {
    final autoplay = widget.autoPlay ? '1' : '0';
    
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                margin: 0;
                padding: 0;
                background: #000;
                overflow: hidden;
                font-family: Arial, sans-serif;
            }
            .player-container {
                width: 100%;
                height: 100vh;
                position: relative;
            }
            iframe {
                width: 100%;
                height: 100%;
                border: none;
                display: block;
            }
            .loading {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-size: 16px;
            }
        </style>
        <script src="https://player.vimeo.com/api/player.js"></script>
    </head>
    <body>
        <div class="player-container">
            <div class="loading" id="loading">Loading video...</div>
            <iframe 
                id="vimeo-player"
                src="https://player.vimeo.com/video/$vimeoId?autoplay=$autoplay&title=0&byline=0&portrait=0&responsive=1&dnt=1&playsinline=1&controls=1&clicktoplay=0"
                frameborder="0"
                allow="autoplay; picture-in-picture; accelerometer; gyroscope; microphone; camera; encrypted-media; web-share"
                style="pointer-events: none; width: 100%; height: 100%;"
                webkitPlaysinline="true"
                playsinline="true">
            </iframe>
        </div>
        
        <script>
            var player = new Vimeo.Player('vimeo-player');
            var loading = document.getElementById('loading');
            
            // Hide loading when player is ready
            player.ready().then(function() {
                loading.style.display = 'none';
                
                // Send ready event to Flutter
                if (window.flutter_inappwebview) {
                    window.flutter_inappwebview.callHandler('onReady');
                }
            });
            
            // Track video events
            player.on('play', function() {
                if (window.flutter_inappwebview) {
                    window.flutter_inappwebview.callHandler('onPlay');
                }
            });
            
            player.on('pause', function() {
                if (window.flutter_inappwebview) {
                    window.flutter_inappwebview.callHandler('onPause');
                }
            });
            
            player.on('ended', function() {
                if (window.flutter_inappwebview) {
                    window.flutter_inappwebview.callHandler('onCompleted');
                }
            });
            
            player.on('timeupdate', function(data) {
                if (window.flutter_inappwebview) {
                    window.flutter_inappwebview.callHandler('onProgress', data.seconds);
                }
            });
            
            // Handle fullscreen events
            player.on('fullscreenchange', function(data) {
                if (window.flutter_inappwebview) {
                    window.flutter_inappwebview.callHandler('onFullscreenChange', data.fullscreen);
                }
            });
            
            // Error handling
            player.on('error', function(error) {
                if (window.flutter_inappwebview) {
                    window.flutter_inappwebview.callHandler('onError', error.message);
                }
            });
        </script>
    </body>
    </html>
    ''';
  }

  void _startProgressTracking() {
    _progressTimer?.cancel();
    _progressTimer = Timer.periodic(const Duration(seconds: _progressUpdateInterval), (timer) {
      if (_isPlaying && mounted) {
        _updateProgress();
      }
    });
  }

  void _updateProgress() {
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    if (currentTime - _lastProgressUpdate >= (_progressUpdateInterval * 1000)) {
      _lastProgressUpdate = currentTime;
      
      // Update progress in background
      _progressService.updateVideoProgress(
        videoId: widget.video.id,
        lastPositionSeconds: _lastProgressUpdate ~/ 1000,
        totalDurationSeconds: widget.video.durationMinutes != null ? widget.video.durationMinutes! * 60 : 0,
      );
    }
  }

  void _handleVideoCompletion() async {
    if (!_isCompleted) {
      setState(() {
        _isCompleted = true;
      });
      
      // Mark video as completed
      await _progressService.updateVideoProgress(
        videoId: widget.video.id,
        isCompleted: true,
        lastPositionSeconds: widget.video.durationMinutes != null ? widget.video.durationMinutes! * 60 : 0,
        totalDurationSeconds: widget.video.durationMinutes != null ? widget.video.durationMinutes! * 60 : 0,
      );
      
      // Streak update is handled automatically by ProgressService
      widget.onCompleted?.call();
    }
  }

  Future<void> _toggleFullscreen() async {
    try {
      if (_isFullscreen) {
        // Exit fullscreen
        debugPrint('OfficialVimeoPlayer: Exiting fullscreen mode');
        
        // For web, try to exit iframe fullscreen first
        if (kIsWeb) {
          try {
            // Try to exit fullscreen via JavaScript
            await _executeJavaScript('''
              if (document.fullscreenElement) {
                document.exitFullscreen();
              }
            ''');
          } catch (e) {
            debugPrint('Failed to exit iframe fullscreen: $e');
          }
        }
        
        await _animationController.reverse();
        
        // Restore system UI and orientation
        await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
        
        if (mounted) {
          setState(() => _isFullscreen = false);
        }
        await _animationController.forward();
        widget.onExitFullscreen?.call();
      } else {
        // Enter fullscreen
        debugPrint('OfficialVimeoPlayer: Entering fullscreen mode');
        await _animationController.reverse();
        
        // Hide system UI and set landscape orientation
        await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
        
        if (mounted) {
          setState(() => _isFullscreen = true);
        }
        
        // For web, try to enter iframe fullscreen
        if (kIsWeb) {
          try {
            await _executeJavaScript('''
              const iframe = document.querySelector('iframe');
              if (iframe && iframe.requestFullscreen) {
                iframe.requestFullscreen();
              }
            ''');
          } catch (e) {
            debugPrint('Failed to enter iframe fullscreen: $e');
          }
        }
        
        await _animationController.forward();
        widget.onEnterFullscreen?.call();
      }
    } catch (e) {
      debugPrint('OfficialVimeoPlayer: Fullscreen toggle error: $e');
      // Fallback: just toggle the state
      if (mounted) {
        setState(() => _isFullscreen = !_isFullscreen);
      }
    }
  }

  // Helper method to execute JavaScript on web
  Future<void> _executeJavaScript(String script) async {
    if (kIsWeb && _webViewController != null) {
      try {
        await _webViewController!.runJavaScript(script);
      } catch (e) {
        debugPrint('JavaScript execution failed: $e');
      }
    }
  }

  // Build web player using HtmlElementView
  Widget _buildWebPlayer() {
    if (kIsWeb) {
      final String viewId = 'official-vimeo-player-${widget.video.id}';
      
      // Register the view factory only on web
      // ignore: undefined_prefixed_name
      ui.platformViewRegistry.registerViewFactory(viewId, (int viewId) {
        final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
        final autoplay = widget.autoPlay ? '1' : '0';
        
        final iframeElement = html.IFrameElement()
          ..style.border = 'none'
          ..style.height = '100%'
          ..style.width = '100%'
          ..style.pointerEvents = 'none' // Prevent iframe from handling touch events
          ..allowFullscreen = true
          ..allow = 'autoplay; fullscreen; picture-in-picture; accelerometer; gyroscope; microphone; camera; encrypted-media; web-share'
          ..setAttribute('webkitPlaysinline', 'true')
          ..setAttribute('playsinline', 'true')
          ..setAttribute('webkitallowfullscreen', 'true')
          ..setAttribute('mozallowfullscreen', 'true')
          ..setAttribute('referrerpolicy', 'origin')
          ..src = 'https://player.vimeo.com/video/$vimeoId?autoplay=$autoplay&title=0&byline=0&portrait=0&responsive=1&dnt=1&playsinline=1&controls=1&fullscreen=1';
        
        // Add fullscreen event listeners
        iframeElement.addEventListener('fullscreenchange', (event) {
          debugPrint('Iframe fullscreen changed: ${html.document.fullscreenElement != null}');
        });
        
        return iframeElement;
      });
      
      return HtmlElementView(viewType: viewId);
    }
    
    return Container(); // Fallback for non-web platforms
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black,
              child: Stack(
                children: [
                  // Video player
                  if (kIsWeb)
                    _buildWebPlayer()
                  else if (!kIsWeb && _webViewController != null && !_hasError)
                    WebViewWidget(controller: _webViewController!),

                  // Loading indicator
                  if (_isLoading)
                    const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),

                  // Error message
                  if (_hasError)
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.white,
                            size: 48,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Video Error',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _errorMessage,
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _initializePlayer,
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    ),

                  // Removed duplicate fullscreen button - using main controls instead
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Method for compatibility with existing code
  void restoreVideoPosition() {
    // Position restoration would need to be implemented with Vimeo API
    // For now, this is a no-op
  }

  // Handle system back button press to exit fullscreen
  Future<bool> handleBackPress() async {
    if (_isFullscreen) {
      await _toggleFullscreen();
      return true; // Prevent default back behavior
    }
    return false; // Allow default back behavior
  }

  // Play the video
  void play() {
    if (kIsWeb) {
      // For web, use JavaScript to play the video
      _executeJavaScript('''
        const iframe = document.querySelector('iframe');
        if (iframe && iframe.contentWindow) {
          iframe.contentWindow.postMessage('{"method":"play"}', '*');
        }
      ''');
    } else {
      // For mobile, use WebView JavaScript
      _executeJavaScript('''
        if (window.player) {
          player.play();
        }
      ''');
    }
    setState(() {
      _isPlaying = true;
    });
    widget.onPlay?.call();
  }

  // Pause the video
  void pause() {
    if (kIsWeb) {
      // For web, use JavaScript to pause the video
      _executeJavaScript('''
        const iframe = document.querySelector('iframe');
        if (iframe && iframe.contentWindow) {
          iframe.contentWindow.postMessage('{"method":"pause"}', '*');
        }
      ''');
    } else {
      // For mobile, use WebView JavaScript
      _executeJavaScript('''
        if (window.player) {
          player.pause();
        }
      ''');
    }
    setState(() {
      _isPlaying = false;
    });
    widget.onPause?.call();
  }
} 