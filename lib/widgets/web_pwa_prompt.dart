import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import '../services/pwa_install_service.dart';
import '../widgets/pwa_install_dialog.dart';

/// Web-specific PWA installation prompt that ensures maximum compatibility
/// This widget is designed specifically for web platforms and handles all edge cases
class WebPWAPrompt extends StatefulWidget {
  final Widget child;

  const WebPWAPrompt({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<WebPWAPrompt> createState() => _WebPWAPromptState();
}

class _WebPWAPromptState extends State<WebPWAPrompt> {
  final PWAInstallService _pwaService = PWAInstallService();
  bool _hasShownPrompt = false;
  bool _isPromptVisible = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _initializeWebPWA();
    }
  }

  void _initializeWebPWA() async {
    debugPrint('🌐 Initializing Web PWA Prompt...');
    
    // Wait for the widget to be fully built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupWebPWALogic();
    });
  }

  void _setupWebPWALogic() async {
    if (!kIsWeb || !mounted) return;

    debugPrint('🔧 Setting up Web PWA logic...');

    // Initialize the PWA service if not already done
    try {
      await _pwaService.initialize();
      _isInitialized = true;
      debugPrint('✅ PWA Service initialized successfully');
    } catch (e) {
      debugPrint('❌ PWA Service initialization failed: $e');
      return;
    }

    // Set up listeners
    _pwaService.installabilityStream.listen((isInstallable) {
      debugPrint('📱 PWA installability changed: $isInstallable');
      if (isInstallable && !_hasShownPrompt && mounted) {
        _showWebPWAPrompt();
      }
    });

    // Check immediately
    _checkAndShowPrompt();

    // Set up aggressive checking for the first 15 seconds
    _startAggressiveChecking();
  }

  void _checkAndShowPrompt() {
    if (!kIsWeb || !mounted || _hasShownPrompt) return;

    debugPrint('🔍 Checking PWA installability...');
    
    if (_pwaService.shouldShowInstallPrompt()) {
      debugPrint('✅ PWA is installable, showing prompt');
      _showWebPWAPrompt();
    } else {
      debugPrint('⏳ PWA not yet installable, will keep checking');
    }
  }

  void _startAggressiveChecking() {
    if (!kIsWeb || !mounted) return;

    int attempts = 0;
    const maxAttempts = 75; // 15 seconds (200ms * 75)
    
    void checkPeriodically() {
      if (!mounted || _hasShownPrompt || attempts >= maxAttempts) {
        if (attempts >= maxAttempts) {
          debugPrint('⏰ PWA check timeout after 15 seconds');
        }
        return;
      }
      
      attempts++;
      
      // Force check installability
      _pwaService.forceCheckInstallability();
      
      if (_pwaService.shouldShowInstallPrompt()) {
        debugPrint('🎯 PWA installable detected on attempt $attempts (${attempts * 200}ms)');
        _showWebPWAPrompt();
        return;
      }
      
      // Log progress every 25 attempts (5 seconds)
      if (attempts % 25 == 0) {
        debugPrint('🔄 Still checking PWA installability... (${attempts * 200}ms elapsed)');
      }
      
      Future.delayed(const Duration(milliseconds: 200), checkPeriodically);
    }
    
    // Start checking after a brief delay
    Future.delayed(const Duration(milliseconds: 100), checkPeriodically);
  }

  Future<void> _showWebPWAPrompt() async {
    if (!kIsWeb || !mounted || _isPromptVisible || _hasShownPrompt) return;

    debugPrint('🚀 Showing Web PWA install prompt');

    setState(() {
      _isPromptVisible = true;
      _hasShownPrompt = true;
    });

    try {
      await PWAInstallDialog.show(
        context,
        onInstallSuccess: () {
          debugPrint('🎉 PWA installed successfully from web prompt!');
          if (mounted) {
            setState(() {
              _isPromptVisible = false;
            });
          }
        },
        onDismiss: () {
          debugPrint('📱 PWA web install prompt dismissed');
          if (mounted) {
            setState(() {
              _isPromptVisible = false;
            });
          }
        },
      );
    } catch (e) {
      debugPrint('❌ Error showing PWA dialog: $e');
    }

    if (mounted) {
      setState(() {
        _isPromptVisible = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Simple web PWA trigger that can be placed anywhere in the widget tree
class WebPWATrigger extends StatefulWidget {
  const WebPWATrigger({Key? key}) : super(key: key);

  @override
  State<WebPWATrigger> createState() => _WebPWATriggerState();
}

class _WebPWATriggerState extends State<WebPWATrigger> {
  final PWAInstallService _pwaService = PWAInstallService();
  bool _triggered = false;

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _triggerWebPWA();
    }
  }

  void _triggerWebPWA() {
    if (_triggered || !kIsWeb) return;
    _triggered = true;

    debugPrint('🎯 Web PWA Trigger activated');

    // Wait for the app to be ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndTrigger();
    });
  }

  void _checkAndTrigger() async {
    if (!mounted || !kIsWeb) return;

    // Wait a bit for services to initialize
    await Future.delayed(const Duration(milliseconds: 500));

    if (!mounted) return;

    // Check if we should show the prompt
    if (_pwaService.shouldShowInstallPrompt()) {
      _showDialog();
    } else {
      // Listen for when it becomes ready
      _pwaService.installabilityStream.listen((isInstallable) {
        if (isInstallable && mounted && _pwaService.shouldShowInstallPrompt()) {
          _showDialog();
        }
      });

      // Also do periodic checks
      _startPeriodicCheck();
    }
  }

  void _startPeriodicCheck() {
    int attempts = 0;
    const maxAttempts = 50; // 10 seconds

    void checkPeriodically() {
      if (!mounted || attempts >= maxAttempts) return;
      
      attempts++;
      
      if (_pwaService.shouldShowInstallPrompt()) {
        _showDialog();
        return;
      }
      
      Future.delayed(const Duration(milliseconds: 200), checkPeriodically);
    }
    
    checkPeriodically();
  }

  Future<void> _showDialog() async {
    if (!mounted || !kIsWeb) return;

    debugPrint('🚀 Web PWA Trigger showing install dialog');

    try {
      await PWAInstallDialog.show(
        context,
        onInstallSuccess: () {
          debugPrint('🎉 PWA installed from web trigger!');
        },
        onDismiss: () {
          debugPrint('📱 PWA install prompt dismissed from web trigger');
        },
      );
    } catch (e) {
      debugPrint('❌ Error showing PWA dialog from trigger: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink(); // Invisible widget
  }
}

/// Debug widget that shows PWA status information
class PWADebugInfo extends StatefulWidget {
  const PWADebugInfo({Key? key}) : super(key: key);

  @override
  State<PWADebugInfo> createState() => _PWADebugInfoState();
}

class _PWADebugInfoState extends State<PWADebugInfo> {
  final PWAInstallService _pwaService = PWAInstallService();
  Map<String, dynamic> _stats = {};

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _updateStats();
      // Update stats every 2 seconds
      Timer.periodic(const Duration(seconds: 2), (_) {
        if (mounted) _updateStats();
      });
    }
  }

  void _updateStats() {
    if (mounted) {
      setState(() {
        _stats = _pwaService.getInstallationStats();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb || _stats.isEmpty) {
      return const SizedBox.shrink();
    }

    return Positioned(
      top: 50,
      right: 10,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('PWA Debug', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
            ...(_stats.entries.map((e) => Text(
              '${e.key}: ${e.value}',
              style: TextStyle(color: Colors.white, fontSize: 10),
            ))),
          ],
        ),
      ),
    );
  }
}
