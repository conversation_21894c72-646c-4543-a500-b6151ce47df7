import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/pwa_install_service.dart';
import '../widgets/pwa_install_dialog.dart';

/// Widget that shows PWA install prompt immediately when Flutter page loads
/// This ensures maximum visibility and installation rates
class ImmediatePWAPrompt extends StatefulWidget {
  final Widget child;

  const ImmediatePWAPrompt({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<ImmediatePWAPrompt> createState() => _ImmediatePWAPromptState();
}

class _ImmediatePWAPromptState extends State<ImmediatePWAPrompt> {
  final PWAInstallService _pwaService = PWAInstallService();
  bool _hasShownPrompt = false;
  bool _isPromptVisible = false;

  @override
  void initState() {
    super.initState();
    _setupImmediatePrompt();
  }

  void _setupImmediatePrompt() {
    if (!kIsWeb) return;

    // Show prompt as soon as possible after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowPrompt();
    });

    // Listen to installability changes for immediate response
    _pwaService.installabilityStream.listen((isInstallable) {
      if (isInstallable && !_hasShownPrompt && mounted) {
        _showInstallPromptImmediately();
      }
    });

    // Also check periodically in case the event hasn't fired yet
    _startPeriodicCheck();
  }

  void _startPeriodicCheck() {
    // Check every 200ms for the first 10 seconds to catch the prompt as soon as possible
    int attempts = 0;
    const maxAttempts = 50; // 10 seconds total (200ms * 50)

    void checkPeriodically() {
      if (attempts >= maxAttempts || _hasShownPrompt || !mounted) return;

      attempts++;

      // Force check installability on each attempt
      _pwaService.forceCheckInstallability();

      if (_pwaService.shouldShowInstallPrompt()) {
        debugPrint('🎯 PWA installability detected on attempt $attempts');
        _showInstallPromptImmediately();
        return;
      }

      Future.delayed(const Duration(milliseconds: 200), checkPeriodically);
    }

    // Start checking immediately
    checkPeriodically();
  }

  void _checkAndShowPrompt() {
    if (_hasShownPrompt || !mounted) return;

    // Force check installability
    _pwaService.forceCheckInstallability();

    // Check if we should show the prompt
    if (_pwaService.shouldShowInstallPrompt()) {
      _showInstallPromptImmediately();
    }
  }

  Future<void> _showInstallPromptImmediately() async {
    if (_isPromptVisible || _hasShownPrompt || !mounted) return;

    debugPrint('🚀 Showing immediate PWA install prompt');

    setState(() {
      _isPromptVisible = true;
      _hasShownPrompt = true;
    });

    // Show the dialog immediately
    await PWAInstallDialog.show(
      context,
      onInstallSuccess: () {
        debugPrint('🎉 PWA installed successfully from immediate prompt!');
        setState(() {
          _isPromptVisible = false;
        });
      },
      onDismiss: () {
        debugPrint('📱 PWA immediate install prompt dismissed');
        setState(() {
          _isPromptVisible = false;
        });
      },
    );

    if (mounted) {
      setState(() {
        _isPromptVisible = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Overlay widget that shows PWA install prompt on top of any content
/// This ensures the prompt is always visible regardless of the underlying UI
class PWAInstallOverlay extends StatefulWidget {
  final Widget child;

  const PWAInstallOverlay({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<PWAInstallOverlay> createState() => _PWAInstallOverlayState();
}

class _PWAInstallOverlayState extends State<PWAInstallOverlay> {
  final PWAInstallService _pwaService = PWAInstallService();
  bool _showOverlay = false;
  bool _hasShownOnce = false;

  @override
  void initState() {
    super.initState();
    _setupOverlay();
  }

  void _setupOverlay() {
    if (!kIsWeb) return;

    // Check immediately after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkOverlayVisibility();
    });

    // Listen to installability changes
    _pwaService.installabilityStream.listen((_) {
      _checkOverlayVisibility();
    });

    // Listen to installation changes to hide overlay
    _pwaService.installationStream.listen((isInstalled) {
      if (isInstalled && _showOverlay) {
        setState(() {
          _showOverlay = false;
        });
      }
    });
  }

  void _checkOverlayVisibility() {
    if (!mounted || _hasShownOnce) return;

    final shouldShow = _pwaService.shouldShowInstallPrompt();
    
    if (shouldShow != _showOverlay) {
      setState(() {
        _showOverlay = shouldShow;
        if (shouldShow) {
          _hasShownOnce = true;
          // Auto-show dialog after a brief delay
          Future.delayed(const Duration(milliseconds: 300), () {
            if (mounted && _showOverlay) {
              _showInstallDialog();
            }
          });
        }
      });
    }
  }

  Future<void> _showInstallDialog() async {
    if (!mounted || !_showOverlay) return;

    await PWAInstallDialog.show(
      context,
      onInstallSuccess: () {
        setState(() {
          _showOverlay = false;
        });
      },
      onDismiss: () {
        setState(() {
          _showOverlay = false;
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        // Overlay is handled by the dialog system, no additional UI needed here
      ],
    );
  }
}

/// Simple widget that triggers PWA install prompt on first app load
class PWAInstallTrigger extends StatefulWidget {
  const PWAInstallTrigger({Key? key}) : super(key: key);

  @override
  State<PWAInstallTrigger> createState() => _PWAInstallTriggerState();
}

class _PWAInstallTriggerState extends State<PWAInstallTrigger> {
  final PWAInstallService _pwaService = PWAInstallService();
  bool _triggered = false;

  @override
  void initState() {
    super.initState();
    _triggerInstallPrompt();
  }

  void _triggerInstallPrompt() {
    if (!kIsWeb || _triggered) return;

    _triggered = true;

    // Wait for the widget tree to be built, then show prompt
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showPromptIfReady();
    });
  }

  void _showPromptIfReady() {
    if (!mounted) return;

    // Check if we should show the prompt
    if (_pwaService.shouldShowInstallPrompt()) {
      _showDialog();
    } else {
      // If not ready yet, listen for when it becomes ready
      _pwaService.installabilityStream.listen((isInstallable) {
        if (isInstallable && mounted && _pwaService.shouldShowInstallPrompt()) {
          _showDialog();
        }
      });
    }
  }

  Future<void> _showDialog() async {
    if (!mounted) return;

    debugPrint('🚀 Triggering PWA install prompt on app load');

    await PWAInstallDialog.show(
      context,
      onInstallSuccess: () {
        debugPrint('🎉 PWA installed from app load trigger!');
      },
      onDismiss: () {
        debugPrint('📱 PWA install prompt dismissed from app load trigger');
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink(); // Invisible widget
  }
}
