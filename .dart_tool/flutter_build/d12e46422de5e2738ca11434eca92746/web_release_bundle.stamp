{"inputs": ["/Users/<USER>/Desktop/66/pubspec.yaml", "/Users/<USER>/Desktop/66/.dart_tool/flutter_build/d12e46422de5e2738ca11434eca92746/main.dart.js", "/Users/<USER>/Desktop/66/pubspec.yaml", "/Users/<USER>/Desktop/66/assets/images/logo.webp", "/Users/<USER>/Desktop/66/hosted_vimeo_player.html", "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/test/assets/images/test_image.png", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/assets/t_rex_runner/t-rex.html", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/assets/t_rex_runner/t-rex.css", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/assets/web/web_support.js", "/Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/fonts/fa-brands-400.ttf", "/Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/fonts/fa-regular-400.ttf", "/Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/fonts/fa-solid-900.ttf", "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/assets/no_sleep.js", "/Users/<USER>/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/Users/<USER>/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.56/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/android_intent_plus-4.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/app_settings-5.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/chewie-1.11.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+13/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.14.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_advanced_segment-3.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-3.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.0+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vimeo_video_player-1.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "/Users/<USER>/flutter/bin/cache/pkg/sky_engine/LICENSE", "/Users/<USER>/flutter/packages/flutter/LICENSE", "/Users/<USER>/Desktop/66/web/splash/img/light-2x.png", "/Users/<USER>/Desktop/66/web/splash/img/dark-4x.png", "/Users/<USER>/Desktop/66/web/splash/img/light-3x.png", "/Users/<USER>/Desktop/66/web/splash/img/dark-3x.png", "/Users/<USER>/Desktop/66/web/splash/img/light-4x.png", "/Users/<USER>/Desktop/66/web/splash/img/dark-2x.png", "/Users/<USER>/Desktop/66/web/splash/img/dark-1x.png", "/Users/<USER>/Desktop/66/web/splash/img/light-1x.png", "/Users/<USER>/Desktop/66/web/index.html", "/Users/<USER>/Desktop/66/web/favicon.png", "/Users/<USER>/Desktop/66/web/icons/Icon-192.png", "/Users/<USER>/Desktop/66/web/icons/Icon-maskable-192.png", "/Users/<USER>/Desktop/66/web/icons/Icon-maskable-512.png", "/Users/<USER>/Desktop/66/web/icons/Icon-512.png", "/Users/<USER>/Desktop/66/web/manifest.json"], "outputs": ["/Users/<USER>/Desktop/66/build/web/main.dart.js", "build/web/assets/assets/images/logo.webp", "build/web/assets/hosted_vimeo_player.html", "build/web/assets/packages/awesome_notifications/test/assets/images/test_image.png", "build/web/assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "build/web/assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html", "build/web/assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css", "build/web/assets/packages/flutter_inappwebview_web/assets/web/web_support.js", "build/web/assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf", "build/web/assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf", "build/web/assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf", "build/web/assets/packages/wakelock_plus/assets/no_sleep.js", "build/web/assets/fonts/MaterialIcons-Regular.otf", "build/web/assets/shaders/ink_sparkle.frag", "build/web/assets/AssetManifest.json", "build/web/assets/AssetManifest.bin", "build/web/assets/AssetManifest.bin.json", "build/web/assets/FontManifest.json", "build/web/assets/NOTICES", "build/web/splash/img/light-2x.png", "build/web/splash/img/dark-4x.png", "build/web/splash/img/light-3x.png", "build/web/splash/img/dark-3x.png", "build/web/splash/img/light-4x.png", "build/web/splash/img/dark-2x.png", "build/web/splash/img/dark-1x.png", "build/web/splash/img/light-1x.png", "build/web/favicon.png", "build/web/icons/Icon-192.png", "build/web/icons/Icon-maskable-192.png", "build/web/icons/Icon-maskable-512.png", "build/web/icons/Icon-512.png", "build/web/manifest.json"]}