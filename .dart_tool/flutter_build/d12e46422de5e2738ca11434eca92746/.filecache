{"version": 2, "files": [{"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib/src/enum_supported_platforms.dart", "hash": "9b7c79f2caa3918c576978f2c2cbf32e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_w.dart", "hash": "36e5b08967f3abd15930bde25e9d2ccb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/permission_handler_html.dart", "hash": "02e6dfb65eefb07c08da31829bc9db53"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/sandbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "893548eaf87a8fd903da6fa761ad5ec1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/awesome_notifications_web.dart", "hash": "ddacefecda044553d11aa27782276227"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/default_if_empty.dart", "hash": "527ad391c229e34074a6d5c1aa656133"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "b29e302994b1b0ea5029734406101b8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/content_blocker_action_type.dart", "hash": "075fab5e03b7aa056039cf647611531b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "50dfb9886f462e2b3405f0f8d23f179b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_u.dart", "hash": "de4ba796e7c200bdc07306e8b82e1f5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_z.dart", "hash": "991a163a470f64b0222de6290e39d538"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/future.dart", "hash": "8d3f31cb53177f3f6315575373249597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/Desktop/66/lib/services/error_handler.dart", "hash": "a503295b0353179dd27f20bc1d93943c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/collection_utils.dart", "hash": "add5f0afe8e318e91950e5725be6f333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/parser.dart", "hash": "e4d5eb474812b6fb78ddb16f9ddb9472"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/LICENSE", "hash": "53d71cd0b0021b18407a0b37287e730f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tree_printer.dart", "hash": "0b59ef1fb417d687f41af0202ba86cfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/navigation_response.g.dart", "hash": "da287a617be09caaeae2bee5d5d2af4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Desktop/66/.dart_tool/package_config_subset", "hash": "b5cd6a55073ddf7031e4743afb6479d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_storage/platform_web_storage_manager.dart", "hash": "6e5a2cb65ddcd1c1b3d9884c5f239ce0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/x509_certificate/main.dart", "hash": "e7418bbafac8f677c251737338a43435"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/data_detector_types.g.dart", "hash": "d00eec9d995d1b5f9a693446fabc23ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/x509_certificate/x509_extension.dart", "hash": "76bf47b90c08df4a463b02114ee65d68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/safe_browsing_response.g.dart", "hash": "a9b88210a16f9a3466f06b6690c283fd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "d9a659644f1b667686f2c9b22545dc0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/magic_number.dart", "hash": "d9d40cd4fd7e692ca4246d952d48cca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime239v3.dart", "hash": "8d69b0d7e796398d342532e8a90521ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_generators/ec_key_generator.dart", "hash": "306b0ac3fd1402ed468c5c00690214d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_not_null.dart", "hash": "929b5628541e8ab826e753c9fe90cd30"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "2c525c85cb323db613ddc5eba4b902d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/on_post_message_callback.dart", "hash": "66f995420c6962fbf120b620e03b8ebb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/angle_instanced_arrays.dart", "hash": "3bb154213ca902f8cce0611f87538957"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_browser/apple/in_app_browser_options.dart", "hash": "a9c4e784d94f309eca914bedddfa6c2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/base.dart", "hash": "62e6826075b4df67271133a79fc4d046"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib/cached_network_image_web.dart", "hash": "7f78986e9338a71ca3aaefe9e5cf509b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/create_window_action.dart", "hash": "9cf499a1e15fd8bb6df25b88d9b35093"}, {"path": "build/web/assets/FontManifest.json", "hash": "5a32d4310a6f5d9a6b651e75ba0d7372"}, {"path": "build/web/assets/fonts/MaterialIcons-Regular.otf", "hash": "acbc5c03e69f9b72030448932d5a965b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_prompt_response_action.dart", "hash": "9cc16f7cc98714d48ab241e1238da4b0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "2fbba4502156d66db0a739144ccce9a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/utils/list_utils.dart", "hash": "ecb6b42ddf900c5fad9146133817bb3b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/firebase_core_web.dart", "hash": "7f9dab0666566be297277974f44dfe6a"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/skeleton_widgets.dart", "hash": "a176f1908bc0dda6be1de7c651b960a2"}, {"path": "/Users/<USER>/Desktop/66/lib/models/purchasable_course.dart", "hash": "c32fd3fbc63cf1e12662803a7f8d7be8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ajax_request_event.dart", "hash": "aa5b8ea9decd6ea7e323be3b1632d177"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/entries_api.dart", "hash": "800ce0cca8ce3af4fd3a21897cfc28f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider.dart", "hash": "f186193f82036b24fc8379b1f332f817"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "91d8303ca1ccc72eccc1ae636c7825ed"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "458f3bf784829a083098291a97123e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/base_notification_content.dart", "hash": "88081c03108bbfc059952fb2e645233b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/octo_image.dart", "hash": "66d6d10e44ad1e696a8e632a5c4883d2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "5c96449c2a494ea8f3a50ecc3ba9af74"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "5be90cbe4bbf72b0264413e4ccb5c275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/base.dart", "hash": "a4eb00bf15ad2af7e8ef8480d7f26a29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/received_models/received_notification.dart", "hash": "f8329638a7dc209758216c16ce488246"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/x509_certificate/asn1_decoder.dart", "hash": "a3d537d1f66974fcdf382ab6f0f8e7f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/accelerometer.dart", "hash": "0436795f780c587c284e98075ee5344d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/cursor.dart", "hash": "5bde4f62a64276d44e1ef4ee3bf194f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/number_parser.dart", "hash": "31c73410cd9adb292ff72d1bdf90f0f7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "f90fd4f8a9988f08157d132c23c8c08d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/extensions/path_extension.dart", "hash": "b13faf802386f562057b4179e7ec9f46"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "2c777edec67bbb084e5608fb5f6b495b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/ecc_base.dart", "hash": "468fe17ab42c185be969ed1eeab7f8db"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "c98d71a32518e80bc7cf24b1da6c9c57"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "d72a4ddaf6162d8b897954e02b4a2a4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/fade_widget.dart", "hash": "e1d33f6f03e359759c131d64cf62c84f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_authentication_session/main.dart", "hash": "c3cc567127b3899c8c561bffe48865f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/auto_dispose.dart", "hash": "ef220252cc1911073575cfbf66f4c8d1"}, {"path": "build/web/canvaskit/chromium/canvaskit.wasm", "hash": "64a386c87532ae52ae041d18a32a3635"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/util.dart", "hash": "50ea252dca474061b6d57ba06eb1f256"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/assets/web/web_support.js", "hash": "3cb6d1dcd278493cb8b8a145aadb432d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_authentication_session/platform_web_authenticate_session.dart", "hash": "33d37ed3d8db108f097c0614d7e466bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/sandbox.g.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory.dart", "hash": "4ce56dab766f683c213da41402d17049"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "f6345e2a49c93090bc2e068a0a808977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/flutter_cache_manager.dart", "hash": "4d339d186836b857e23b70679d7544c4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "44927d8a4e3825e7c3be0af91307d083"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/chrome_safari_browser_menu_item.dart", "hash": "0f57f6109c4f6b3193ce3e6f2fd9eb29"}, {"path": "/Users/<USER>/Desktop/66/lib/utils/video_security_helper.dart", "hash": "32c8865f742148f3d9094a31f6905f25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system.dart", "hash": "9007580fb76ae011692307f00e0a28f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/wasm_js_api.dart", "hash": "9a3ffc11698b5af44402167cded39432"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/fixnum.dart", "hash": "ca96fbf1a27d4f30ff02bfc5812562a6"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/progress_card.dart", "hash": "1723dc61ad272cecab13eab0da2d7e7a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/services.dart", "hash": "bab8606629135509c96d78f7253526ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ajax_request_headers.g.dart", "hash": "ddc13949cbae29c1e1c4437d61d846a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/notification_life_cycle.dart", "hash": "5216aff2bd5d7777ad49540355292c3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/isolates/isolate_main.dart", "hash": "eb3c0a05aec73d565f36266210c25e74"}, {"path": "build/web/assets/packages/awesome_notifications/test/assets/images/test_image.png", "hash": "c27a71ab4008c83eba9b554775aa12ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_message/web_message.g.dart", "hash": "1da9f1ce9fd163fa972979d4a4965300"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/ripemd320.dart", "hash": "a7cad8a0ee91928a961ab734fa6ff3c8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "6e320dd3d12f0e125541bc4b983dcfa7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/combine_latest.dart", "hash": "0c4028018783c732ca451e7fff693d3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/netinfo.dart", "hash": "fcc009cb2fb000be4e3c251e9777f7e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_media_size.dart", "hash": "fa63f6863563b712d020e986e6fbe6ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/custom_tabs_relation_type.dart", "hash": "7f76aa524817f3379619f6b0b38af828"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "c97a8ffd51479d05a18a54ac27ccba15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/pragma.dart", "hash": "871c4029c43c6dcb8ac9ba8f7799d310"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/oes_texture_half_float_linear.dart", "hash": "8e5a3b57694eb6cde651f6cc2cb72fef"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "498db9e29a08e6fdc8aee5eeb4d204ce"}, {"path": "/Users/<USER>/Desktop/66/lib/theme/app_theme.dart", "hash": "6de441346358b333a26a901b3d7f7434"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "ec2260a55dbb3ff283297d9da97e130c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_storage_type.g.dart", "hash": "58be9e99fefff7add8f02e21c7cd54b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/font_awesome_flutter.dart", "hash": "5bfead188f731f9d4ad7d50ece0a0bf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_import.dart", "hash": "e0a5a25c69f7362ae3d6e493dfc611ee"}, {"path": "build/web/canvaskit/chromium/canvaskit.js", "hash": "34beda9f39eb7d992d46125ca868dc61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/public_key_parameter.dart", "hash": "f95ded997d4466677aec73999d61f614"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/awesome_notifications_platform_interface.dart", "hash": "ad92cd12aa50d69147294c807358de69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/inappwebview_platform.dart", "hash": "b72e4fb343a1c76c830436dbbf515634"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "883b210f4cc20daebdb2834dbe4a512c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_digest.dart", "hash": "7eced463b747e7992e0e4a5ba21023ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/lib/src/closed_caption_file.dart", "hash": "c2a1ab7ce77b50b86b6f785052280a48"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "900a13c9fcd73f4e8e3d069d76af6ffa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/trusted_web_activity_immersive_display_mode.g.dart", "hash": "ea540acf3175400951daf2525016bc46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/src/connectivity_plus_web.dart", "hash": "7e7b862f5743afd3383eb4c18d0d887d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/underline_style.dart", "hash": "fd7dc6b262072199d68ea8b46c532341"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/visitor.dart", "hash": "9cc453290a0fea4e24b848a74967c59b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_alert_response_action.g.dart", "hash": "50fd54dc7c94f546349e03cc22b387e9"}, {"path": "/Users/<USER>/Desktop/66/lib/providers/auth_provider.dart", "hash": "9c700d8afaf2620f300a395a47aa9b04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/kft_bottom_navigation.dart", "hash": "1657136a469e5f9e4f1eee7756d6f04e"}, {"path": "/Users/<USER>/Desktop/66/web/splash/img/light-2x.png", "hash": "fe25924a52b5bfc49b6587ffe0d54f53"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_variant.dart", "hash": "ce58628e17748af44a93e252b9c54d1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/html_escape.dart", "hash": "efc823416c4e5e4dcced4cc2c3bbd89c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/fetch.dart", "hash": "7bc189c041a9af516afc4cf06fa04a48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/LICENSE", "hash": "1c52a06a48033bea782314ca692e09cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/impl.dart", "hash": "dc83669d5e8c3f6966706169ffe4c8a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_frag_depth.dart", "hash": "d02fb3624a4fb2e006c88c8f598e3daf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/context_menu/context_menu.dart", "hash": "787d0f8f95b89be18f67b7ca5aede474"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/scan.dart", "hash": "acfc0a55deec22276e085dae6197833a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/macs/hmac.dart", "hash": "68e9d53e365e08c6729d99c4f16a94de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/scatter_chart/scatter_chart_data.dart", "hash": "cc6fd3891167b86785b2c0bbcb786ff0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/android_resource.g.dart", "hash": "07253c82b94420573202f40c8f4c3b31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_tracing_controller.dart", "hash": "ac12bdd49175752bf7f56e6ba8860640"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/ccm.dart", "hash": "41f36ff57afc9c456e29620d8e0d8efc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/content_blocker.dart", "hash": "54529633cf618af2ab60b425ccd2f4de"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "16859f5e798cf33fc3c76a7a3dca05d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/helpers.dart", "hash": "9e1daba981bfab0a1424950a97970ca1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/chain.dart", "hash": "1112185143b6fe11ce84e1f3653b2b6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/permission_resource_type.g.dart", "hash": "95d91469c2aca5a0d7e67d1efb678f94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/layout_in_display_cutout_mode.dart", "hash": "696f4519a0b0d6d9b6478e99dbf97a59"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "aef544fef0ced7679e0edaf5f8d036b7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "59475498db21e2333db54d6478af7c94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_webview/in_app_webview_settings.dart", "hash": "e3bfa43f9bfd8df94a8231c1077806e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs1/asn1_digest_info.dart", "hash": "11a61d462d75bfb40a3e011486a042b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose.dart", "hash": "a57c7d0bb0b0f3ff52fd48c953453bd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/exception.dart", "hash": "e625c15c91736f7c029d22eaf13a9599"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/html.dart", "hash": "2a74c03dd6b0f0c721c3366d8e646c05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sql_builder.dart", "hash": "1c4127d99af22e5232df8132ae79beeb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "34ebb85f7f2122d2e1265626cf252781"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "6b48e1348ae677efad30c0a9d4600e38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_compressed_texture_etc.dart", "hash": "406426872f004adaa359fd9697e46d32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/src/icon_data.dart", "hash": "51baa35340f57beaf4b5c16ff2a1191a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/bidi_formatter.dart", "hash": "5c81dd07124ccc849c310595d9cfe5be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/ripemd160.dart", "hash": "946665f8d69b449931369c446519f3b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/race.dart", "hash": "2164e0e3bc00d90bd03708ddfd475ad9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/tracing_mode.g.dart", "hash": "e042ff1ba349bef1e69bc0d24569a8e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permissions.dart", "hash": "1addb41b1ec88a6b5674bd3486e9e225"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/connectivity_plus_platform_interface.dart", "hash": "88d5feb6f0a1ddf0cafe75a071bbcab2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "03d585dfc6055d74a4668e69263afa5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/notification_channel_group.dart", "hash": "097e42091b5960166d0a37bd86adf2c7"}, {"path": "/Users/<USER>/Desktop/66/lib/programs_page.dart", "hash": "adf6a6e3226387405ccc8c8a44141910"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/http_auth_response.dart", "hash": "50bbd6dc08eed3d5c35efec19433d05b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "eb115c2e8f0ff170bf26a44efd1b5c05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl2.dart", "hash": "12494b6f15f091477d72a97539e343c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/nested.dart", "hash": "5c621d343831cbb9619557942e6b7d9f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "17a28a030318e2c8f8fd653e0b862d50"}, {"path": "build/web/assets/AssetManifest.bin", "hash": "976247ea7c7623d361bc2c0bc5c89dfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/google_fonts.dart", "hash": "885d6001f197c05de34b17e76acc7ed4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_compressed_texture_s3tc_srgb.dart", "hash": "475963783287cfaf98b88b0438997e21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/sic.dart", "hash": "5ce7d9fedfa2c40efbf00c41224759dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/mediacapture_streams.dart", "hash": "888f5d95b09ab34de2c9d37bd7a33077"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/trusted_web_activity_screen_orientation.g.dart", "hash": "8e1a11e9b321f31471a5b335458ff9c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/enums.dart", "hash": "b49758f50c20a4f98a48e3af42de35d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/async_provider.dart", "hash": "3a2d20718f772fbb710aec7dc5e0bf80"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "0434e70443094435172ff3d214d26bba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/auto_dispose.dart", "hash": "34f75dd4788c48a2a7b2ce2efe4c51fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/chrome_safari_browser/main.dart", "hash": "64d7f850c711b760c055d0ccf65d8612"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/axis_chart/axis_chart_scaffold_widget.dart", "hash": "88df1acce44d63d2400c17ad921f1fe5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "6c0e97a3b04c9819fe935659014f92e8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "7b0e6dd1794be4b575ecf8af6475f0e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/config.dart", "hash": "6e1f276f9f7416f792db31fd51b3e3ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/notification_category.dart", "hash": "cd6d6de9944f7820c9c42f61899ee86e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/lib/screen_protector.dart", "hash": "ad4e5c59626ee07919f170cbb6487d3b"}, {"path": "/Users/<USER>/Desktop/66/lib/services/optimized_image_service.dart", "hash": "c018e2a3bce9361d5d6bb5dd373301fa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "2bc2f148be8fffe5f3a6a53fe8bc8333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/action_type.dart", "hash": "da0e04288dd44c59af7d910a959c1a65"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "bbc7eccdbd8472a2180e0dffce323bb9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "73089c9737db54a05691e09bc9fc1bcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/background_sync.dart", "hash": "8274d7a1aa4341e38d8c81b9b16ba5e0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "15957b9d3eac4a2e1acaa24a3032afe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/constant.dart", "hash": "176c6b2c4f4e2d64cd55df2a0dabe5e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/permission_resource_type.dart", "hash": "f9ba8018fe49a36d0bc403393b1fa203"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "5979a1b66500c09f65550fab874ee847"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/html.dart", "hash": "75bb30a58c7ea909b421ab34f056fdbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/queue_item.dart", "hash": "937dad14a7958c57948525533b199296"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/sample.dart", "hash": "98ad95f9d48fa93a9cdc4a8fa0f69c73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_family_with_variant.dart", "hash": "1562c4a8bfee3d68c041674517ef436c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/gcm.dart", "hash": "bb64ec5f608bf82d3c275c3978893381"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "e27d4685e9e6aa906547a77095cc1ac5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "a2ab6e0f334e5a28af29766b82f7f4b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_webview_feature.g.dart", "hash": "3a0fb6e5dc94e91207d94ff1fb2f7010"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "34371da200382409d181bf9c3fcaefc7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/frame_info.g.dart", "hash": "d6880a11cddcd8cbfc7db972569120d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/trusted_web_activity_default_display_mode.g.dart", "hash": "3ab5cebd009bb3b5015219c38ce2db2c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "1d893e6d648c41d8e3281a76a2320431"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/largest_contentful_paint.dart", "hash": "422496814972d30f353aebfaa10ba3ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/hkdf.dart", "hash": "7f593a221d1320305917631f622a5ba1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/lib/src/model/macos_device_info.dart", "hash": "2dad016b21ffd8671999ec7fee53d20c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "95f488b1875988eb094e0ba71deb7deb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ovr_multiview2.dart", "hash": "4f4be543ee7b471b82757e405a2e9356"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "8c1a2c1feaeb22027ba291f1d38c4890"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider.dart", "hash": "a6705b39e0c01e2fc0e40b8c8c674aac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/platform/platform_web.dart", "hash": "a0f927ed88e035f7ff0080e30d9a8b08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_padding.dart", "hash": "dae7ae92007717d576679bd62bb2ce47"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "166147b7bee5919995e69f8ca3e69d17"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "94c0c017ccb267b7cacc7c047ee5b9c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/fetch_request_password_credential.dart", "hash": "0e64d400ffe8cc7df4a8fdb40b4b072f"}, {"path": "build/web/assets/packages/wakelock_plus/assets/no_sleep.js", "hash": "7748a45cd593f33280669b29c2c8919a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/load_request_params.dart", "hash": "c652b89f16c2597e1c3278b1e66e513a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "4eb84c94445470d8bb6bb8e2666aa51a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "fddd73db94bb2fa3a0974bed845f32a8"}, {"path": "build/web/splash/img/dark-2x.png", "hash": "fe25924a52b5bfc49b6587ffe0d54f53"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "2d3948bf5dd7b63d100270fce62fa2d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/oes_texture_float.dart", "hash": "d5f7267a21029dd081e33d87f5a0661e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/style.dart", "hash": "825ec1b2847bd00ad5cd840c7ddc4d6f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "7f2ccd6eece375fce2e247d3995e45c5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "81fd3ef494f4443fb8565c98ba5a9ba2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/url_launcher.dart", "hash": "10bbfa83fe7c3c8f8a4964a3e96e5b58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/json_cache_info_repository.dart", "hash": "f8113503c91cd843d744fa61b0b15ba6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/keccak_engine.dart", "hash": "4b47b39739cb8185654484fa9a440d41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "737642bf1a2d9ebd63c82016292b6b93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/method_channel/method_channel_firebase_app.dart", "hash": "4f4575a514eec25990a9923547e2ac28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1util.dart", "hash": "6163f4485dae643d418359623a0a8e7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/method_channel/utils/codec.dart", "hash": "020552519d966b598cd3bb17849a3a49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/web/web_platform_manager.dart", "hash": "d1f26af3937be041df986277ff7fa407"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_n.dart", "hash": "8fe95cebce3f522e41f0bef51a1818b7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "96b4be28e9cb48156c65de35d7ccefba"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "ec48414c6983150c30241ba7128634fa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "a103dff72cbe4ef64a02c37dbfdc752d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib/src/exchangeable_enum.dart", "hash": "18b8dc500aa67103e1acb65baffe80ba"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "61137458bbcab0dfb643d5d50a5ae80f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ajax_request_event_type.dart", "hash": "c64020e259f5ede666ef05897ac297c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/ecb.dart", "hash": "ea046544fb5b729368584b6b1b2888a1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "98f06a29791e4f6ffc1ccefd18f323fb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "ea5bbc17f187d311ef6dcfa764927c9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ajax_request_action.g.dart", "hash": "eb51b2be8c8eb90dfa5b53f7708c56fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_storage/android/main.dart", "hash": "0e850fb5da721066e0599ec36bd9d451"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_message/platform_web_message_channel.dart", "hash": "3552f00548d8d1eae654ada337d41c2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_proxy_controller.g.dart", "hash": "e415d20224ffc12b3db685e38bcb951c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/axis_chart/axis_chart_data.dart", "hash": "c0ba6726126fcc071e45bcd248912f60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/base_chart/fl_touch_event.dart", "hash": "c8ba4ee305acb51fd51c8090fe306816"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/awesome_notifications_method_channel.dart", "hash": "a6f9a968cedfe0bb0eae7ca9252c5669"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/scrollview_deceleration_rate.dart", "hash": "4dce7ca53b7608b2bcde323eacc09f93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_pkcs12_attribute.dart", "hash": "3801b5a1c67d9ee7d645423ea830bb0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/server_timing.dart", "hash": "fcbb7d84b5581cb366a304d13a9d957b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/axis_chart/axis_chart_painter.dart", "hash": "3f366386aee5b9242527c2ca2619c140"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/paddings/iso7816d4.dart", "hash": "913f9b2e892f99726d2ab6210f2f6819"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/intx.dart", "hash": "c3e3bdde1f486b799e08a1ed1b99c76a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "1ae1a412c9f9daff34b9dd63e60cec2d"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/courses_page.dart", "hash": "df01814078dd170f785a0b356a206e8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_page_order.dart", "hash": "95e5cd77f686078b2807dcaf70da9d3a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "123520ee3a48eebf4ba444e93436bb1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/pie_chart/pie_chart_helper.dart", "hash": "d53e5e29157046a01f222df89f73a1e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/default_cache_manager.dart", "hash": "8ad6f50f623fbd97c2aa23d86d3c22ad"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "b9bfa2dc31960df2f1fd3aee88c3807e"}, {"path": "/Users/<USER>/Desktop/66/lib/services/device_compatibility_service.dart", "hash": "5624582007657fc75e753d8d8f81bfa8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/notification_localization.dart", "hash": "e4001051c8e4e134e624eefd94759a43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+13/lib/interop/analytics.dart", "hash": "b1fcdf4c4066f77f26baae1272c09280"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_protection_space_http_auth_credentials.dart", "hash": "60b0628d45cc65f129ff4d2fc9b66980"}, {"path": "/Users/<USER>/flutter/packages/flutter_web_plugins/lib/src/navigation/url_strategy.dart", "hash": "038969861ff07119d70df079da581e5d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "58707cf455f97f907192b4ff92d36711"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "42c4c0281ec179aea5687dbced56aca7"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/kft_button.dart", "hash": "f9d7aee75a1eb27d82ab3ea398ab658c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "18223495a47aa96889552c9834042729"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_authentication_session_error.dart", "hash": "0a7de94b36ee2cb9ad168bc92c4db2e6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "0a546a51fffe9612c8c3cbebc609691c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/request_focus_node_href_result.g.dart", "hash": "f4ca32fa15f2a74889161c6e2b23630e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/search_result_display_style.g.dart", "hash": "518921bd8986de6446eed41b990e5226"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_protection_space.dart", "hash": "c824ec1a68086889d96ca532fc09d0a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webcryptoapi.dart", "hash": "77fda802f54858a88d7535227bb1ebc4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "ca3df05f249dbc5a38ebb86ee9a74a1e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "ed5548873fcf5a0a5614fc52139600b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/date.dart", "hash": "86b720af61fd71f6566c9e8d42412e85"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "8807672a31b470f53c5fcc2b36dcf509"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "964f3ee4853c34a4695db0c7e063eaa3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_browser/in_app_browser_settings.g.dart", "hash": "d804233c74ac1c7af32e06f3ed2cad7e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat.dart", "hash": "eb8f82998d7328c46b04354df987a331"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/utils.dart", "hash": "7a6fe2bde5e3bf653cd473308d8402c5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "39f5f34a4d3615c180c9de1bf4e8dde8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/trusted_web_activity_immersive_display_mode.dart", "hash": "16b636f3163c3d81edf4b132523004c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/registry/registry.dart", "hash": "4b40445b5d011f54f19508bf46bc4ba8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_webview/apple/in_app_webview_options.dart", "hash": "afce6a105d9465fda7e5766d644373e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/in_app_webview/android/in_app_webview_controller.dart", "hash": "46ec1fa53eda055b5374a0eca9c25cfb"}, {"path": "/Users/<USER>/Desktop/66/web/icons/Icon-512.png", "hash": "ce3d4a1216bdefdc14a07019b7fba41e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/env_utils.dart", "hash": "d75f62f03297d8fada84de77f3e92373"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/selection_granularity.g.dart", "hash": "3bd14c21ace7bf9ebf6b91d9d6fa9b2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_ia5_string.dart", "hash": "0edffacf0034337f57cd291eb488a52a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/standard_component_type.dart", "hash": "09973ba0a94d2d819052c0544dcdce70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/url_launcher_uri.dart", "hash": "3cb04add978cf19afa2d0c281e4c80b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+13/lib/utils/exception.dart", "hash": "674fcc2c5a68e177a87b2c80f7afb996"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "93d025adfc0409629c51036cb0fdc085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/server_trust_auth_response_action.dart", "hash": "a54ecdc5dc4b5031f17f5157465832ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/in_app_webview_initial_data.dart", "hash": "1e35a08d4d6db42387e2b585fe9cfe33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/create_window_action.g.dart", "hash": "8605ce852c4d9dbce248f34363508cbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/method_channel/method_channel_image_picker.dart", "hash": "13b37731f32d54d63ecb4079379f025b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs10/asn1_certification_request_info.dart", "hash": "a4aab76e29682587c7cb505b1b1c9fc4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "f59aed120736d81640750c612c8cfe5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/timer.dart", "hash": "24a365985ef5e526e029d73522f4f2fd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "525e57b6ade38da2132c8ddb0ea78547"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/proxy_rule.dart", "hash": "0d7bfc1b27b253148c47731b380829fa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/_bitfield_web.dart", "hash": "0e8cfaa51c02ccb73c6dcb46e3743882"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/in_app_webview_hit_test_result_type.g.dart", "hash": "0b752350fb6f20bc6beb8d211430889e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp320r1.dart", "hash": "383f9fc1ec6fea8eed35b8be5538e8e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/Users/<USER>/Desktop/66/lib/models/course_settings.dart", "hash": "f7ccef118df40cde9249983508b7ae94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/notification.dart", "hash": "b9609815ffdb79eb76f2cd3a77a60906"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "785eedcc96fa6a4fcc7c81a8736a7427"}, {"path": "/Users/<USER>/Desktop/66/lib/providers/quote_provider.dart", "hash": "491e632199a41ea71220308e87bbbfc3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "e5163b554926dc261b556dc5d94245d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/shared_preferences.dart", "hash": "698b47b813b0194cf3adacff5906a585"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/download_start_request.g.dart", "hash": "1c88c80a802c948b76fda8774f6c006b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "7bbb6aab4e83fc272886a39c92157201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/proxy_scheme_filter.g.dart", "hash": "b4b3177ca7978b5f7abac7e83372f05c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_disjoint_timer_query_webgl2.dart", "hash": "9596f92640ea1703dd10aaae0a28dde5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_animations_2.dart", "hash": "f56db1857dbcbb843dd89b7f55db0815"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "6f18c18a1a5649f27b6e0c29dfba4dc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/main.dart", "hash": "0541231051042d5a1489ab8699a4bfae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_message/platform_web_message_listener.dart", "hash": "5eee5cb3b1499073cad1c608ee106dce"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/store_page.dart", "hash": "68b329da9893e34099c7d8ad5cb9c940"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_enumerated.dart", "hash": "8ad5dccfca7e8306466bc4d4edf46a1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/axis_chart/side_titles/side_titles_widget.dart", "hash": "2f3a4a8d509adddff7c5544512ee1f13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/lib/src/video_player.dart", "hash": "5509e5328c2824ef5776804432b03bc4"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/home_stories_widget.dart", "hash": "4b4ddeb8f53820fce93f6f757bf35a61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "hash": "bfc483b9f818def1209e4faf830541ac"}, {"path": "/Users/<USER>/Desktop/66/lib/services/video_streak_service.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5/LICENSE", "hash": "1eadf5be3116dc31e5f04d4d6d352497"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "4da5ad5941f2d5b6b3fbb3f7ea217b41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/method_channel_package_info.dart", "hash": "5489bd1170add17f6d3bcc248b5ed048"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/algorithms/rsa.dart", "hash": "4c852dd5fa9ed3fc4d85a1582632f568"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/web_delegate.dart", "hash": "c2f3dc321195ebd6a9972cca519f3aba"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "bbc9542eb5e3c4701c24bc1268b8165c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/call_async_javascript_result.dart", "hash": "704db6d0b12099e5e75e5b12118e7c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_method_channel.dart", "hash": "2c294b86e9cf73bb732d8419ab47f434"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "3ad691d7f4e0dfc9bac177f56b288925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/force_dark_strategy.dart", "hash": "0ebae45e0e09c1e27bb56cb51b2f0897"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_credential.dart", "hash": "b0f0ed34f160eb58e975a36acb0a52a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime192v3.dart", "hash": "d85a6c479a410094f7ee24bd2084b481"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp512r1.dart", "hash": "760c33d66faa9169929f8c8d005222ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp192k1.dart", "hash": "******************************36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_browser/main.dart", "hash": "c27d1c2c141c0848807f839cd76312de"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "ac317f8ed3b04bec644817e6f60a28d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/fetch_request_credential_default.g.dart", "hash": "9afbb703aa8b096c5ac25a32ef91ef47"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "c06267b6c315a5e40f28feb6019de223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/des_base.dart", "hash": "5dbf70d09e29734788c4dfe7bd9a08bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/consumer.dart", "hash": "f28a95b717859fa14ea8344e766e7fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/test/assets/images/test_image.png", "hash": "c27a71ab4008c83eba9b554775aa12ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/lib/src/web_impl/import_js_library.dart", "hash": "e3eebc9f374a69df792846dc41f41cea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/should_allow_deprecated_tls_action.g.dart", "hash": "db6326a912616f6d7a803a20714dc593"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/webview_asset_loader.dart", "hash": "6b014f85292284013117aff033a124a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/connectable_stream.dart", "hash": "fcbda87916b8b2c4f7b88460ac9bb415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/compat/file_fetcher.dart", "hash": "808718676ca459f8941aa519a73bbff2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vimeo_video_player-1.0.1/lib/vimeo_video_player.dart", "hash": "f2da5d39e9a14a9e6998eeb01597e90c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/asn1_encoding_rule.dart", "hash": "16b69ff904d37271ee8f4af2b5621f7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/unsupported_object_identifier_exception.dart", "hash": "68dba5a68c15abb96aa390afe73d789f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.56/lib/_flutterfire_internals.dart", "hash": "09004088d4048afe4f54ef5c78ffe98e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "7c07d5cc739ae29abcfbf6343ae84fdf"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "3d7501e746aaa83cd9cc1b508d3f7ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/float_formatter.dart", "hash": "9193766efadfc3e7be3c7794210972ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_resolution.g.dart", "hash": "86ac066ca3da089a6957fbbccc64e75a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_before_unload_response_action.g.dart", "hash": "51bff8c963f7ad7b2e6c5cff4fd94722"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_web.dart", "hash": "71b9fd89c14d2a8a39275d81a2500c5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/exceptions/awesome_exception.dart", "hash": "0977874b91f2ab653b45ba2868bd8a49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/image_picker_platform_interface.dart", "hash": "b152cc1792a66ac4574b7f54d8e2c374"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_authenticated_safe.dart", "hash": "7dea8c8f45a4d1fd5f2d9113ce7228c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/disposable.dart", "hash": "d93d980d3f14ae03dca5fb25690340c2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/scribe.dart", "hash": "d195153a8c01a0392b38e3b9adc672d8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "db4a14227247e2524e46f6b0dd9da267"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "5d7b0ee48c302285b90443514166c2d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/flutter_riverpod.dart", "hash": "05100b6f82b19ef0bab59f9f174ad39e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/screen_wake_lock.dart", "hash": "02b2fa04e8c4cd7b45c9b4e3d477e339"}, {"path": "/Users/<USER>/Desktop/66/lib/screens/system_health_screen.dart", "hash": "7e482f3b002064228d6583dbf3382f3b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "511ff5c6f0e454b22943906697db172f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_alert_response.dart", "hash": "74fff211df4270301bdce2037fedfe95"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/long_sha2_family_digest.dart", "hash": "6b4b2978d38db2ed3aedb6f0f62d6946"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_authentication_challenge.g.dart", "hash": "696a75f28e289cb2c8e6c25f931f93da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0/LICENSE", "hash": "2a1d7a43556be673c919adbea08c91d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha1.dart", "hash": "39d5994f96295109bb5aabf8b30e7d37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/utils.dart", "hash": "9d122acee9d1f43dcdb2ea88fd1fc95f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_message/web_message_port.dart", "hash": "2ee726d2b7f79d58c1097cf24848cc3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/asymmetric_key_parameter.dart", "hash": "e0a46b7ab15db018858fb7ed96b75785"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "73f043194b9c158454e55b3cafbdb395"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp521r1.dart", "hash": "2ed0d22935079b89bbfcb6141b37999e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/LICENSE", "hash": "3db7ca942613a5abfdb502781bc65fcf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/url_launcher_platform_interface.dart", "hash": "9190f2442b5cf3eee32ab93156e97fb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/parameters_with_random.dart", "hash": "fff3d52d873f92db7ebe6915f5a933c4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "036fc28dc98388abec4456e8142c530f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/search_result_display_style.dart", "hash": "f355f5bda50b3bc0cb5cec60b9d28df3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/about.dart", "hash": "4bf9cb0fbb8b0236f0f9e554c7207a4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/saa_non_cookie_storage.dart", "hash": "9ba73a099cc9ea4f64804786f0b64d0d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/trusted_web_activity_display_mode.g.dart", "hash": "249b142ac0e50ee8feaf36ddde90f7ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp160t1.dart", "hash": "eb845be8c283dbdc0d511ab93679d402"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "11fc97acd20679368ae2eaa698c6f130"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/token.dart", "hash": "a27310d4435c84885993bedb05adabfe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/oes_texture_half_float.dart", "hash": "a8b21e7f9e07675ace0ab0adfb3a9f99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/tracing_controller.dart", "hash": "d57ef4b391af4ef0924abf68a0a1e332"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/definitions.dart", "hash": "6bbf37fdfca478a440984a08602baf8a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/server_trust_auth_response.g.dart", "hash": "e0792c75cbc6d8591e3d76485c2a7286"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat_eager.dart", "hash": "28788651dbafca42ae0d6023352274f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/base.dart", "hash": "d7d24730943cbf47d39aa11425ebf344"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1boolean.dart", "hash": "e4ad456a21e3a2abf87b2fcaf963d6ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/bar_chart/bar_chart_helper.dart", "hash": "2c9ffb15a86fca5051306bd78ba28036"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "790dc5e1e0b058d13efbd42a3f46498e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/received_models/push_notification.dart", "hash": "c6b89b425b597b92a512a8360dbb5cbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/src/interop/utils/func.dart", "hash": "4c4ffe81e8442ea9526b0795503e2951"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/enhanced_whatsapp_support_fab.dart", "hash": "4f55eb3b94e2be1a5e1fb6e3e461fffd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/options.dart", "hash": "e64d63aabc0975a7e9fdb384598c2f8f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "bda2eeb24233fd6f95dc5061b8bf3dd5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "f26e2cb53d8dd9caaaabeda19e5a2de3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/lib/wakelock_plus_platform_interface.dart", "hash": "20fd07d2430529e29af6988e3aeb545c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/safe_browsing_threat.dart", "hash": "fbe6c5f08d51f14dd5436e3d530a59f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/apple_options.dart", "hash": "d4efda9ec695d776e6e7e0c6e33b6a4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/_web_image_web.dart", "hash": "11448d08e398579152d5206e8d935d85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/web_helper.dart", "hash": "60db0a181494c7db06a18464e2d6e796"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp160r1.dart", "hash": "18f36ed422693224b3733071e73c25e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_request_attribution.dart", "hash": "c3278ab198c1b9114d1c8333cb755705"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/debug_logging_settings.dart", "hash": "36ee3333bf4af4ee2166c2991c3265f6"}, {"path": "build/web/assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html", "hash": "16911fcc170c8af1c5457940bd0bf055"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v7.dart", "hash": "eaeef30b0e3cd638d4dad2b0f4db8417"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/dom.dart", "hash": "6a64fecc9f1801803c9a6706f60ad958"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "c679063104d2f24639459c8ab3eed77a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/base.dart", "hash": "0ab8c6ae2a539e1eee8cc8e4e7cac2d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl_helpers.dart", "hash": "c0f563a80ccf76ce9e15cb224b221cc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/padded_block_cipher/padded_block_cipher_impl.dart", "hash": "36341e026a32e13852f8dc3f7ba49f5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/parsing.dart", "hash": "16d4d82628956a3b88ae5de8480aae49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib/flutter_secure_storage_web.dart", "hash": "04cdbe07d74e2562b528d19fcefab662"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/permission_handler_platform_interface.dart", "hash": "54e3fc58f0992b887be63771a3d82202"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "3d892f04e5e34b591f8afa5dcbcee96d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/encrypted.dart", "hash": "f226d8b0fbba304ed6bbca081a613971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webrtc_priority.dart", "hash": "4a6d26f0dbca3a5a449047a11471ac54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/platform_webview_cookie_manager.dart", "hash": "1255d62f0a7be683fe89db6256ef8610"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_async.dart", "hash": "255fd9cb9db57da2261cb7553da325ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/window_titlebar_separator_style.dart", "hash": "f6e7327336692a99aace58ec49cbbbc8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "fab9f5f0fb3bdd9295e12a17fef271c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp256k1.dart", "hash": "dfb565e96434d9969a6ae0a093e7a40c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "98772211ffa69a8340f8088cd7193398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "f6816ebd27db772616d01f543b33d0f8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "97359ca5bc2635f947e7616f792565c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/script_html_tag_attributes.dart", "hash": "6d200223f69768879f8620b40dbafef1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/server_trust_auth_response_action.g.dart", "hash": "87984018c61ee6d4318c895a69b3b306"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "f350db07fdddbcfd71c7972bf3d13488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/element.dart", "hash": "d414c1f995780a939e1d357efa0400a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/request_image_ref_result.dart", "hash": "8dd6d5454d66c7e390a2bc247278a163"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/cross_origin.dart", "hash": "0888c9535067e817e8d48b3ca875531f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/cssom_view.dart", "hash": "a6df205ba9fd0ce49f7d0884d1f02b33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/random/auto_seed_block_ctr_random.dart", "hash": "0e016aed1674f8e3cbc23abe63d5f108"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "0fa4800227413041d2699ed47918c7f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/attributed_string_text_effect_style.g.dart", "hash": "f9a8626f080c168bd8bf09e73a3ff0f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/platform_check/node_crypto.dart", "hash": "ad5946d4373b3c8170540fcffddfd478"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "48b13baf494b39e894252da0a0f6e8c0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "3c24303086312d7181ffa10d0521029a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib/src/utils.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/server_trust_challenge.g.dart", "hash": "5c2d7fc91caa319072ed764bdc2a2dd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/family.dart", "hash": "751c8376ab9bb4a866f5db6d7e6b864b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_e.dart", "hash": "012c3b670fb153803ce635838e1fa9ae"}, {"path": "/Users/<USER>/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "ef5fc00d685cd2a36c4de80e1c7e3a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/window_titlebar_separator_style.g.dart", "hash": "2905e5e5368716656bed7a1e10d64ce7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_webview/platform_webview.dart", "hash": "3cb4fea0bb308ec42c0039fbea105103"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "e78589269f033237f43ffdc87adc47a9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "be66f00d2c9bb816f4236dd0f92bff55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/constant.dart", "hash": "84fdc97cdb402f94c301f5154682112f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/lib/src/model/ios_device_info.dart", "hash": "fa912830d66b4a0179fc3f22563ce6fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/algorithms/aes.dart", "hash": "6aab3b5c1f46dbe2da208d348f8376fc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "b0aac7d00e469646d25550d1e4e77d12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.0/lib/src/pigeon/messages.pigeon.dart", "hash": "039d5cab001e9eda6c447ae8a9032e0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/gostr3410_2001_cryptopro_c.dart", "hash": "c849cfe9b5fed1c48d8eb868fbd5e4ee"}, {"path": "/Users/<USER>/Desktop/66/pubspec.yaml", "hash": "8d12bfd3a17ed9a889f1f526f2044c31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/property.dart", "hash": "9f56fbe65bf797a2eba907e0181e69ca"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/_capabilities_web.dart", "hash": "9055e5d2c7c065d122848e2eecea896d"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/workout_page.dart", "hash": "9b6626917b0cf5ad648635aa095ba3d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib/src/reentrant_lock.dart", "hash": "227fa2ff622f28df7ac1417cc0bbeed4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "a91b4b0d0d10b955e8973126cf288ea4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "72bbc3da5da130fb11bb5fc65614653c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/print_job/print_job_controller.dart", "hash": "e9595e7431f4578d1b618bceb695430d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/server_trust_challenge.dart", "hash": "f42a5c4f148fc32c52c21540ff4f383a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/aes_fast.dart", "hash": "052847524e2484cdfad41d0cca0904e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_credential_persistence.dart", "hash": "7120a8f13929d1e6d36450a01143e0e1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "12a21ff35182c138908274c8b66714d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/Users/<USER>/Desktop/66/lib/config/app_config.dart", "hash": "e09b95859b2e2f21d13fadb85e2a137c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/credential_management.dart", "hash": "721ef479b7a4fcd21729b0acd4cb2669"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "c7d65c476f653e952aedcb0cbcab3c73"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/pkcs5s1_parameter_generator.dart", "hash": "bb37e91f19a10c42b2e8a514f16c3c6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/value_stream.dart", "hash": "4ec7181b3b281703a8fddee43b540ee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_last.dart", "hash": "08f42ef74f129fde820b3414026b8d34"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "8e7a18cd739e24a264facecc38379085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_texture_compression_bptc.dart", "hash": "c5759bd6693e3553630b0e87e474e133"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/android/chrome_custom_tabs_options.dart", "hash": "b460321705642114f9c8c42465d792e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "98f725d06ba20a1032cb8770d00d7fca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/webview_flutter_platform_interface.dart", "hash": "0503ce57073fbbfc3fa9a0139f3e5d97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_info.dart", "hash": "2f3e8198efb4b9ec92c0126b25986acc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib/src/jsonwebkey.dart", "hash": "656cf6ccbb38540c63f99381c4b24c90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_generators/rsa_key_generator.dart", "hash": "c5ecf74912c76ddf0eb32a599ce4ce0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/padded_block_cipher_parameters.dart", "hash": "b5c7af5d480c2e01c449b0be049b20cc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "28d3a26c44687480bac3f72c07233bf6"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/comprehensive_video_player_page.dart", "hash": "08d45bcbde656ba99753ea95f8e3e900"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_descriptor.dart", "hash": "df2373fa53c57974996330429774683f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_web.dart", "hash": "ef96dab0228c9a2166808e0c5f95ca47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/premium_animated_logo.dart", "hash": "6acf03be634aa7acd093db45cc839663"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webxr.dart", "hash": "389e1f91987c62edc204aeedee11875e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webrtc_identity.dart", "hash": "d41bf06a3f15451f68bcc24768c5c5d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ssl_error.dart", "hash": "54a55ae1caa0c2ba2a7dccb3531380a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/media_capture_state.g.dart", "hash": "1ccc5c87d09ed655d0c8e712ed238654"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "2cb8483d7aa2b998d4641e25a0425f67"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "0f717ff4ecfdaa0347894abbedd5d1e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/web_resource_error.dart", "hash": "4a496da43ada30e10f2394e8f52f4fa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/Users/<USER>/Desktop/66/.dart_tool/flutter_build/d12e46422de5e2738ca11434eca92746/main.dart", "hash": "e95c1239d839edaeab816a8760cefc64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/cache_managers.dart", "hash": "d4a2cc62bec6dff9fcdc94bc868ea014"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with.dart", "hash": "46c6500112cb203a46608825824d4d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/service_worker_controller.dart", "hash": "88e7b4a1e1b3ed950b014d724427fb3b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/lib/share_plus.dart", "hash": "07d60aa100b4ebcf5bd603008d425e91"}, {"path": "build/web/favicon.png", "hash": "969c51f58546ac38d88e3bf319cccd66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ajax_request.dart", "hash": "8150a46b022cb524d138416601734ad5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/resource_timing.dart", "hash": "7a1d80d3a6b17fab735111e172ce99d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/non_storing_object_provider.dart", "hash": "21cb059be81989938ccfbda405ae9a65"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "91bf94aea1db708a8378fa41de066d33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/subjects.dart", "hash": "1f923e6c3ab08753130b61ba27256959"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/LICENSE", "hash": "9741c346eef56131163e13b9db1241b3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_webview/apple/main.dart", "hash": "c30b75b4d34f9489824b72586d116ca2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib/src/exchangeable_object_property.dart", "hash": "0fd14daf59ae046817ea1ffb4b21123b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "f979a94d7bd35cf2a5168fbfb9bdcf1f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "26312d25d45c45d94edcfbaaec9217b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webcodecs.dart", "hash": "7f7e5fa40c1f82049989d2691da38e0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/pbkdf2.dart", "hash": "8fc2d5e1cc5209087a568408c74049d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/LICENSE", "hash": "8367e3c321be234bbc0ee94c177b178e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_object.dart", "hash": "470452529b3925fdb9a5865578558e83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/factory_mixin.dart", "hash": "47258dc751a1217744986101e934f62c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/api.dart", "hash": "28b7197a537fe7660eb8cbb1a789a9cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp160r2.dart", "hash": "4199638bf85e837947f14a023bba818d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/sanitizer_api.dart", "hash": "8d529a9c9b9eb4ebaf4051f92166372b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/platform_interface/platform_interface_firebase_app.dart", "hash": "209399f0e6f16675c3f087b8eb17087b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ui_event_attribution.dart", "hash": "67be3fcfe54c878ff1577a242a41e4b9"}, {"path": "/Users/<USER>/Desktop/66/lib/services/quote_service.dart", "hash": "f4b7207c2c50bb1552447c985d62843e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/macos_options.dart", "hash": "ef56d0c30c2ebbf770de5c7e9cd6f6a7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "db6f70d83d36597cc6bc3eaaffd10aaa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "9a977b88944bf59512e9d8aaeef93605"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/entropy.dart", "hash": "6e92b2107eef3d9fd4ceace7819a66f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/frame_info.dart", "hash": "90cb3d9d5631dc0afb9846f843f43adf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/android_resource.dart", "hash": "f922bb99c3698198ab7fc3d717ef08a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/fl_chart.dart", "hash": "2ef48c86b3ce7fb556bef04e55e3bb56"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "88dbcce51623c5bb2cbe1e4a0f80a902"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int64.dart", "hash": "da07db909ae6174095f95d5ee019d46c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tree.dart", "hash": "01d34f3007e4fddbaf4794395c4d9276"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/publish_subject.dart", "hash": "b2fa768bd42261fab936524dd6f1c8ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/path_utils.dart", "hash": "e335de991d295627ccaabe152db13f68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/src/web/dart_html_connectivity_plugin.dart", "hash": "98d4aa9164b2f8c0bdec648ec8d76c33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/base.dart", "hash": "737fc999d5d26218c34c7423fe061f1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/web_resource_response.dart", "hash": "9df794779fc351159011a56c9d173bc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/extensions/side_titles_extension.dart", "hash": "c024f0b097ca90ea66fbb8097be98b26"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "c5e44030289c2c25b26c5b3aa843b3cc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "ff7c5f41b6493392c45ef30383f6af9b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "0e13760edcb9f90f659ba77c144a3461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/client_cert_response_action.g.dart", "hash": "d38cb1a37d0444ccacd92d96ee8cf160"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/media_capabilities.dart", "hash": "d2e6e8548dd35829a6198324074055a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1constants.dart", "hash": "16dbb6f22fd8703424ee8f6d5338d911"}, {"path": "build/web/splash/img/dark-4x.png", "hash": "7520625a040b816f2d8ec86ef8b5af52"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "826b67d0d6c27e72e7b0f702d02afcec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/find_interaction/platform_find_interaction_controller.dart", "hash": "e53eb7cc59287822c753bace3e24e16f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_delegate.dart", "hash": "35512e89f2b31322744090b018902bab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/services_impl.dart", "hash": "a6d82f072fbaf76b1276861d20c1b788"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_response.dart", "hash": "957eccc8f07afcc65ae916e0263cd873"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/clock.dart", "hash": "d52c28f679ecf880a21c3ba08df59267"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "e3b1d07a31d08470207f2b668564a833"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webrtc.dart", "hash": "287e157d179a7159895d685607ff445f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/lib/src/model/windows_device_info.dart", "hash": "93faa63229d86e5b66c6fa4fd0554f1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/gyroscope.dart", "hash": "9cbb8f979e1c128e4df7a7fb9e8bd7a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/browser_client.dart", "hash": "cc781b4a7a4b4498f839f39f72c73d06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/emojis.dart", "hash": "a988839e63353b3e6ee0ba14ebf17434"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "hash": "092362603d55c20cda672457571f6483"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "8199cdd8c075bef2ed0811394702680d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/token.dart", "hash": "f81e0f51e6529eaf92d4e8d6196e4e13"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "82d1200fedba087f85961d6b1b9332fe"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "b656f459fa4dd04f817455858d3dd20f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "d7c9baf97f1348c00c56f8d64a3ce53a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "0f61d8c0c0870ae724b64f2f2af816bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/selection_api.dart", "hash": "ef86635f28c74edbf20990a9c867ebbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.0/lib/src/platform_interface/platform_interface_firebase_analytics.dart", "hash": "cd84dca9834100cce934ba0202f5797d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/line_chart/line_chart_data.dart", "hash": "a27dd9e2c7c2320b691213fa36bcdff9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/default_ringtone_type.dart", "hash": "ea155a23211f183d9b454ee67b4409a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_message/main.dart", "hash": "3a84d3d9adebf1c0498e9e93a977da02"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "0119e0f7758ee8ef19baeae2b96cb389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/micro_money.dart", "hash": "391b7eda9bffdd4386292eae157d449c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/LICENSE", "hash": "35889fcd28be68a600d7f4ff44c4388e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "9ad11b4bdb179abe4ccb587eb0e2aebc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/notification_channel.dart", "hash": "012fe421f4ad06a5f6f500b549195b71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/awesome_notifications_web_interface.dart", "hash": "3049ae25eb6e9766f271ba6a43b5cf38"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "0c46b12a4e0301a199ef98521f0ed3ab"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "12120b49ba363d4c964cf1d043a0aa1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_debug_renderer_info.dart", "hash": "4155ef1accbeb110c862d616f2a2ad3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/pull_to_refresh/pull_to_refresh_settings.g.dart", "hash": "cbf6f4323f14628d4cd5fc5982d7b9f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/window_type.g.dart", "hash": "bbcd1c095142b71a0a11319dbe00587d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "b3465d5b02dd4743d8d9f9e4170a1151"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "fad2940dc1f4f3e4a0ebb5c7ff40a3a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/extensions/paint_extension.dart", "hash": "dd7ee8a3c9794545518a723f0de05216"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/arg_utils.dart", "hash": "9812b8e536c69068c0e5f3d3db20c140"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_key_bag.dart", "hash": "6c8b0130bb90390a5219ecde2cda29c4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "38c6297c7e2085554452d28299d29a09"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "982099e580d09c961e693c63803f768d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ssl_certificate_dname.g.dart", "hash": "607b627612f187db1e52e56811c81b7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha224.dart", "hash": "100657e7d45f57cd685d0e1a0d9a0143"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/frame.dart", "hash": "49286617067167600a8c7357dff1dcfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_prompt_request.dart", "hash": "bdac8af7b01de23ef1adeabff0f07873"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "e1a148a465b713a6366d5a22a1425926"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_state.g.dart", "hash": "f343c210a8959aab9f76f579c5564790"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with.dart", "hash": "f95bd67282cf610843bb37b5784f3eae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/chacha7539.dart", "hash": "52c8a27d05b6c19fb1478aee4c69a6d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/pull_to_refresh/platform_pull_to_refresh_controller.dart", "hash": "b2839ba762f64a3ba046becbe7e3d147"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/max.dart", "hash": "82294310993896043f681e7fd66c4e56"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "355538055d623505dfb5b9bae9481084"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_utf8_string.dart", "hash": "83bed422d5a5a22b1b980ba4470bbdf7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ajax_request_headers.dart", "hash": "8632d02e120b2be1ef31852656ddcb9b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "fb23ec509c4792802accd10fa7c8a6b0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "7d5bd66d61c58afe63c6d33ee0e421c1"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/mini_course_card.dart", "hash": "617d7a92aab258ec87e775a4bd140309"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/csp.dart", "hash": "a91a10d47bd8bc0b0647fbfb09173dd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permission_status.dart", "hash": "e644eae6cf851b3c46f83af266811a6e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "1fd7c932679011d491315ff136d13822"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/method_channel_flutter_secure_storage.dart", "hash": "20e7221c12677486628b48b0c30569f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/context_menu/main.dart", "hash": "62fccf2fb68cc5fd0790eb70533f9943"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/Users/<USER>/Desktop/66/lib/services/optimized_video_service.dart", "hash": "a8aecfc93d237a5ba4bf1b7f928a48c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/speech_api.dart", "hash": "a6378f15238416e3ee0f731025017a99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/utils.dart", "hash": "4904f65d68a0e23943366dd258084968"}, {"path": "/Users/<USER>/Desktop/66/lib/design_system/kft_theme.dart", "hash": "206d353098b906a2155bafa12c16785f"}, {"path": "/Users/<USER>/Desktop/66/lib/services/performance_monitor.dart", "hash": "4c51b794f1fff93907dcd46dbdefee3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/min.dart", "hash": "ecfd8a09746c8bbb7b51d4741fb4645e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "6ea409faabc2d30760053a8936e45796"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_protection_space_http_auth_credentials.g.dart", "hash": "b5b310d44f911cf96112bc49efeef277"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0/lib/video_player_platform_interface.dart", "hash": "c1c3f2403d5906a6e87fd8d2ecf14c04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/content_blocker_trigger_load_context.dart", "hash": "802a6c0c65505e8c775ac44575fa0205"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/asn1lib.dart", "hash": "5d418a9e868070cb636b7b95a3c0be7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_uri.dart", "hash": "4f5cef960e85ff76e669c442062ade4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/zip.dart", "hash": "636229be247a1ecd50a669eb2dc73206"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_disposition.g.dart", "hash": "be74f8dd47603de75dc3e86d999e00ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/date_format_field.dart", "hash": "71a8fb28c6cc831bc9bc7c636575765b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "1303bc77ad63625069f2d23afc73f523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/picked_file.dart", "hash": "90a070dfee5777a4bca169be4bda3bb1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "2be9783170f41208ab65361d7cb0ddc4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/fs.dart", "hash": "8793ac2a7158951b613820f6a44dd355"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/navigation_timing.dart", "hash": "a842a5f8a2b5ab393b7d7e063c962b16"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/srp_server.dart", "hash": "3e4b026bb9c6dfce5a669a3ee88c0a7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_webview_asset_loader.g.dart", "hash": "6da5713eabcff9b3f1bd54551017c6a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_x.dart", "hash": "8c0609f71af975bf4d5197e6e0352a40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_alert_request.g.dart", "hash": "75644d9b69b9ba70955c9b1787cdb6cf"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "c8a14f8ecb364849dcdd8c67e1299fb3"}, {"path": "build/web/assets/assets/images/logo.webp", "hash": "fabcd654c28a0e5216ae3a8093046520"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "e892b3496135877dd5a0ea2ea2fc91e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+13/lib/interop/analytics_interop.dart", "hash": "5b59fa970f74a95e6adcfa90794b8227"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "b269f9d6378b540b7d581db466ad98d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/in_app_webview_rect.g.dart", "hash": "20f51f8648d86e4df681cc19ad0f772e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose.dart", "hash": "7c89e8d3e17b2ff04570b741ce311e44"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "ff2b2e7159e19374f968cf529da25c01"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "2610f7ca2c31b37ad050671aafbccdd9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "0f6f972f6232b9d18cf00a9fa432127b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/helpers/events/events.dart", "hash": "61a9113d5f96e171950654b239f000d4"}, {"path": "/Users/<USER>/Desktop/66/lib/services/session_manager.dart", "hash": "df6bb523d9261cc1a87fdc3d0e9557d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/pull_to_refresh_size.dart", "hash": "0ffd11886d044986fd4d9a85ac239986"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "2adcbf9fb509dd8fe8864a702db29043"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.14.0/lib/src/port_mapping.dart", "hash": "d1870b4415ddf7f379e6e41b520ca299"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/foundation.dart", "hash": "b4a0affbd6f723dd36a2cc709535c192"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/scatter_chart/scatter_chart_painter.dart", "hash": "d0bb1cb014087be69ca4acdd3cc8b39f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/intl.dart", "hash": "6bf6753f69763933cb1a2f210f3e7197"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1octetstring.dart", "hash": "dbb97fd48268228ae8d8952d0779863e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ui_event_attribution.g.dart", "hash": "f41e49ebbce7ee09fe705714b6b9638d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "c165bb259eb18a2dc493a0e7a1d1ebd9"}, {"path": "/Users/<USER>/Desktop/66/web/icons/Icon-maskable-192.png", "hash": "ad7efca123ce585fd13a3d034d7348c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/export.dart", "hash": "18b06ed8e19ec8469c043edec467ebc7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "49194534260502aa020910c20fb3ad6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webauthn.dart", "hash": "016492ab3715179209a3c8648fb4665e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "4e04af41f89adf9231bad1579f5bb9a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_page_order.g.dart", "hash": "832ae6fd0b91a0475904d9bfc59285bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/asymmetric_key.dart", "hash": "e866e738f5ca40c36ac1afe2196066fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/LICENSE", "hash": "53d71cd0b0021b18407a0b37287e730f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "bef4f4d150af7d7e46b13da4847f86fa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "e37bb4fabbf2e61e9b7fbe06f5770679"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "ca759e06438affc7dcbdd9c4d8f0dbb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/media_source.dart", "hash": "19e9e75b805121b8f916a22696c1d82e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/lib/video_player_web.dart", "hash": "7653959137e6a4e8013e293df9053043"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/src/image_resizer.dart", "hash": "7bfedcd5c37b27517f6407ff837ba5a5"}, {"path": "/Users/<USER>/Desktop/66/.dart_tool/flutter_build/d12e46422de5e2738ca11434eca92746/web_plugin_registrant.dart", "hash": "d71bfd105700570df7fc32b4be3b9f0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/geolocation.dart", "hash": "fd88a6bfed6b081f6305e8f99c178be0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "cad4582fa75bf25d887c787f8bb92d04"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "6f3424f2fc515abb888590b75c98e190"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/extensions/text_align_extension.dart", "hash": "59f0d9fa64905482ce8f6532d57426aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/sprintf.dart", "hash": "9c00cbf52bb0297fccad0b5c5b54d4e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/lib/src/web_vtt.dart", "hash": "7e6d63944d605ab693c41a006835f541"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "62cbf59e5c816c224ef5eaf803fc877b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/extensions/gradient_extension.dart", "hash": "6d29d42fee2653502b8b37c78ca91b1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_resource_error_type.dart", "hash": "db25d1a1206f9f60df2f0e466f479247"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "8e58a1e955460cf5a4ea1cea2b7606cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/webview_environment/webview_environment_settings.g.dart", "hash": "a2ad39288cb4001880f04819464de953"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "956c84257f1efe6f10ab24f3d6702307"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "045c779ec8564825d7f11fbbd6fb2fa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/value_utils.dart", "hash": "c112ad2acb33c46fcd09f4f2b7d2675e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/attributed_string_text_effect_style.dart", "hash": "5b2ea20577f4949599c06c8c4e4dfdea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/compat.dart", "hash": "8a31d0709de6865d3f49374ab6bb274a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/src/interop/utils/js.dart", "hash": "32d7494ac5fe490682fec998abeef183"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "e472fd233266592e97b3fb39bb1a11dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry_when.dart", "hash": "add862853473647f3bae9dee0b365857"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "5af6304445e6664f6caca9ed4b5e885f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "a5d0509a39803ffb48cae2803cd4f4bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "777aca422776ac8e4455ccc7958f7972"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.14.0/lib/src/firebase_app.dart", "hash": "fc8837c1b0c22211799e9412e64b08a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/pigeon/messages.pigeon.dart", "hash": "27609fef75714481627c2ef33c2eb952"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/vibration.dart", "hash": "5e1dd34b3c889f65885f5175968648b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_resource_error_type.g.dart", "hash": "c52eb30c07f89c25be976ea29e7c4e42"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/chacha20poly1305.dart", "hash": "f470c01fea76057e483eb3148f90083c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0/LICENSE", "hash": "bf2ce082976b40a77b9be405d83dad59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/cached_network_image.dart", "hash": "dd2d618db009ed3aa82488ca3b0e1261"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.14.0/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_d.dart", "hash": "2a101a9f7dc3955fa1a1cb93fde33565"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp224r1.dart", "hash": "27d5ca1f907901597d5d2430f6a6ba31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/web_resource_request.dart", "hash": "50a76a0eb65096e74dda3f418407ce75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/tracing_mode.dart", "hash": "cb49eece26399c6da8faa4d87ff0d3ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "e45c87e4aadaebf7ba449f4c60929928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/subject.dart", "hash": "4b93fc559e6626b4d42e924b10c58678"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "940daf4491e3ab2e15d7eac5d6ce6b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_darwin.dart", "hash": "644e5e32abaad61eb192128f412424ed"}, {"path": "/Users/<USER>/Desktop/66/web/splash/img/light-3x.png", "hash": "83bc7b765cca0d749dee3db9e14a0405"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/webview_render_process_action.g.dart", "hash": "3ef3354b02d560b249319f1f51a9b9c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/on_error_resume.dart", "hash": "f79083ce7919dc45b4d2c313bd37af7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_lose_context.dart", "hash": "ee954c303b5a0b6a262df5dcce771a1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/merge.dart", "hash": "c2b88768bdc9704848019fd9df8c2546"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime256v1.dart", "hash": "0057ceeb1027cb9f88a06733e0ea96f4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "0c38ab3123facc4ec6f01ba31158c3ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/mime_shared.dart", "hash": "c2f30f0829e63ccf0449de5982e324b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/http_auth_credentials_database.dart", "hash": "13cc7cd87dfcb7f1fb1bce489458134a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/stack_trace.dart", "hash": "9a478fed4f2f15993c892e33f6fd766b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/algorithm.dart", "hash": "97c871650da560b2968a14f9aa55c1a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs8/asn1_encrypted_data.dart", "hash": "9de044db0c3ef6ec8d505dea6c6fd9d4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "270de9c98f9c1284da0a6af9176ee1f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_pagination_mode.g.dart", "hash": "afd4132fc16f4d72a1ee5a3b558b8870"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_request_network_service_type.dart", "hash": "4bd4e89d49b490eefa677afeae42c9f1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "14177be7a74b321668af2b9effa0f396"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/bound_multipart_stream.dart", "hash": "8e2dfba570ecd1895c50258939b609a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_compressed_texture_pvrtc.dart", "hash": "96ea44a3916958ce0ae07a66485cb12a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/fetch_request_federated_credential.g.dart", "hash": "0a37d3fc0a3f7347c910ed9f5d51318b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/number_format.dart", "hash": "6cad3d78b208ef8a929f29c2628224e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/Users/<USER>/Desktop/66/build/web/main.dart.js", "hash": "8e80473078df96b0d995beab956af266"}, {"path": "/Users/<USER>/Desktop/66/lib/providers/course_settings_provider.dart", "hash": "e7cd5ff043d0b263be42b1f67160a0ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/subscription.dart", "hash": "8ab19033cc6a918c1e4f454495a9ab5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/line_chart/line_chart_renderer.dart", "hash": "27e338ab5edeefc6d04fe6117d2c3bfc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/fullscreen.dart", "hash": "8ce1ef239f773dbbb83a136ef8da4560"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/custom_tabs_relation_type.g.dart", "hash": "de1c4e5381f5ab8d17510924329f2989"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/asymmetric_key_pair.dart", "hash": "2d62ac6ca4d4859bcf344bb57579b632"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/notification_layout.dart", "hash": "0a86a00a564fc3a446c4c1c1a6fe8079"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/web/headless_inappwebview_manager.dart", "hash": "35c84192f468e979ab234ddf6b29a17a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/trust_token_api.dart", "hash": "25c47fc47f8f474488e3d0c9f9806cef"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "7b28ec35aed9cbc3319bf4c15d7b352a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/provider.dart", "hash": "08fb5f27432143c416f473db763fa8c6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "384c15d93757a08ae124e6c2edeb4e9e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "e4a748e0ab7265def948ce2f5dbce86e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/optimized_profile_image.dart", "hash": "356cf6046009fab06c19fb7302acc98b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/platform_webview_controller.dart", "hash": "7be81e300ad4e6ea17c68526cf3f1684"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/group_sort.dart", "hash": "ebbb36fbbb2afcb4c0d783580a3db886"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/md4_family_digest.dart", "hash": "024f7bd028389820eceb3c964a114141"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/exceptions/isolate_callback_exception.dart", "hash": "ec0d39fa7adae0dcc7bbe5e0482adfa5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/rendering.dart", "hash": "4bd3950a0bf4a9f9b09f97594e363d36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sqflite_logger.dart", "hash": "6745a4321f65340dc91faae80415984b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/window_features.g.dart", "hash": "8b65ff87af8838eac857a95496a1c90d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/text_direction.dart", "hash": "45f61fb164130d22fda19cf94978853d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/find_interaction/main.dart", "hash": "0937754a4c15504dd0d69382263b7ab6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "c8f69577793923bfda707dcbb48a08b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_fonts.dart", "hash": "a26d8d16b5f7d1052db1c0c8cbb1f8d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_resolution.dart", "hash": "529e1fe758eaa22a260e49ab41fe7647"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/sprintf_impl.dart", "hash": "2e7ac5275644c470359f8b69c555bfd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/windows_options.dart", "hash": "b4355b7f9f9e50017ce52a8bda654dd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/service_workers.dart", "hash": "74202a148c536b1b659ab009beb77d23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permission_handler_platform_interface.dart", "hash": "d3e01a9f299b192bb14b18062c49e562"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/scrypt.dart", "hash": "0d695a7dfb1bdede54b449bc703d346d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/constants.dart", "hash": "3b481084198e4581293dd9ddddb9afb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/ofb.dart", "hash": "78e2885cda7b8ebe22bf138d66086847"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/object_identifiers.dart", "hash": "b1218847a912b31d0ce265943e8f6a20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/apple/safari_options.dart", "hash": "6697845e09875f4e2d9a169661a9fdfc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/group_by.dart", "hash": "6c3232594edbc47bd6ec36d04c194a9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_random_access_file.dart", "hash": "576c23d693f7712935103974ed9312ee"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/widgets.dart", "hash": "9f9b1fcdf4037b3b4c71ed65b57e87f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/hr_time.dart", "hash": "b48b79ddcad91a15f6ed332a695af619"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/fetch_request_credential.dart", "hash": "c4b5c0b7740d99506b5c0eec5ec2140d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asymmetric/rsa.dart", "hash": "36da076a780a3a0d705082abb04835b1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "72c318c3499a7a4d533965d32c6dface"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/src/image_resizer_utils.dart", "hash": "502f8453100bb00f42598f413212f412"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_credential.g.dart", "hash": "e3c30c475210ab29ab23891e62095f87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_protection_space_authentication_method.g.dart", "hash": "d27db1bc1ed1b507e983d6d140e894c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_request_cache_policy.g.dart", "hash": "2c1eed6acd946da387fdb03ad4dcc474"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/http_authentication_challenge.dart", "hash": "24ff989abda3a9f02d44e558a61d2418"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/encrypter.dart", "hash": "94375912ec651e0d394d4991f6c61d5f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "0575a78fbb39a292302737868752da77"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "0bb85eff209a2008dc5f47b2beda5bf3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/using.dart", "hash": "f70fdb6ec3125a8d6f6fb5ea4cbac59a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/stack_zone_specification.dart", "hash": "d0268b4d80612385359eadd2d6ddb257"}, {"path": "/Users/<USER>/Desktop/66/lib/services/bulletproof_auth_service.dart", "hash": "50ba81fb346b2434914c5dc4ee219514"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/modal_presentation_style.dart", "hash": "98b9748f2d35544d0e431055d0fe5e63"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "9b76b249fb23172215a62d66bb393ed5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_to.dart", "hash": "a28073e1b0a1ffd4999c24379f1dfe02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose_family.dart", "hash": "c8b1bc30159a132cab814de0d71e0462"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/behavior_subject.dart", "hash": "86d361932e590380696b3189090d1034"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/safe_browsing_response.dart", "hash": "d2a2428bf658f19a1a034046d8193e15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/base_chart/render_base_chart.dart", "hash": "1b2756e3837187e5c96b213b5b5800ec"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "e38cc213f0e4b4ed76471f4d70e20abe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ssl_error_type.g.dart", "hash": "d329ed8314d75356df2f6e92162e28d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/desede_parameters.dart", "hash": "29fcb9fa6ab76c0fff43a12a248c46ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/website_data_type.g.dart", "hash": "b58f7590dbcfcc8b5372cb13567229a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/scatter_chart/scatter_chart_helper.dart", "hash": "abad5fe66b5a94db45de5bebd5c81fe9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "f1c0b135f35af022771e30409953e0f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/platform_interface/image_picker_platform.dart", "hash": "38982dc702bc4583fd29314508a32c17"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "3f2a39352a1c6067566f8119aa021772"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "269af8ca7030ccfd9c868fe9af8a6b0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_browser/platform_in_app_browser.dart", "hash": "232abfbffd1cfc7197c8dcfb0c6b554c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/extensions/color_extension.dart", "hash": "2afc847f7335d53a8f1ad08b42ac598d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/printer.g.dart", "hash": "588f3dc0ba872bdf9d86695ca84fe639"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_utc_time.dart", "hash": "cf8bc581794031043fe663a10b541530"}, {"path": "build/web/canvaskit/canvaskit.js", "hash": "86e461cf471c1640fd2b461ece4589df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_texture_compression_rgtc.dart", "hash": "541fce8c5326dac6975fa2876b00a710"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/force_dark_strategy.g.dart", "hash": "3aca6c5bae8b0efbf8787f9289a4e4cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/api.dart", "hash": "72b70b9814ae3e83df7a4c09ac2780d0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/_web_image_info_web.dart", "hash": "9abc752a418b2f274f283af79c10a5b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "02f1d44813d6293a43e14af1986519ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/safe_browsing_threat.g.dart", "hash": "86e664036d8a50b3cebff885283437e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/meta_tag.g.dart", "hash": "c014260ea5b701f0ef28f22f13b148cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/fetch_request.g.dart", "hash": "517d1f15a64a0af0c575c93daf2f710a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/geolocation_permission_show_prompt_response.g.dart", "hash": "b3dae60c1db7f4b285f73d5e06b842ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/platform_webview_controller_creation_params.dart", "hash": "b5a4bd755f0d3a44d4f4d0e8cddd26ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_confirm_request.g.dart", "hash": "e7ec03aa0fd9fd959c806334ad79f93c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_color_mode.dart", "hash": "7d379d4c2a517253863a91a7ff6124be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/throttle.dart", "hash": "12faaaa2952e6917c271f5dbe9cd6bab"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "97f7922aea45c38413930285b604bf18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/pointerlock.dart", "hash": "292b2f9e18932510b27c2a138aa2c6df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "hash": "06d63878dac3459c0e43db2695de6807"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/dismiss_button_style.g.dart", "hash": "c8f2c272fe08e56eca6d0ef48d428b02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_webview/in_app_webview_keep_alive.dart", "hash": "183f289faa5995d551df956011b6aa4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/builders.dart", "hash": "dc1a141705a29df814f129c65b47b5d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_while_inclusive.dart", "hash": "389552e6852c3214ca6857ddadb7cd0b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "b3019bcd49ebc4edd28b985af11a4292"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "094b2c03ad4e0ef5bc1144e281142b2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/permission_response.dart", "hash": "bcf302957044f326e7d14ac7cb2a9fa5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/registry/registration.dart", "hash": "75cbf5c83dd2ed0ab6067971eddb1829"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "eabd3dc33b1a3a2966fa68f6efeb6bce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_before_unload_request.g.dart", "hash": "42b791f69bd87b9afbdc24a813fc9efd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "c2dcf2bcdc85d007f9729621d13cccf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/web_helpers/web_helpers.dart", "hash": "bb9e04644b6d2ed527d5df1b8523dc85"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "2fe7a1026669f97031a83f6da44d248b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/line_chart/line_chart_painter.dart", "hash": "bae5fe3873fde3cdeeae6c2d51b6e0e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_asymmetric_block_cipher.dart", "hash": "feeb432aa90175168f140209d26a4e83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webmidi.dart", "hash": "3ac71c621e176bd5ffd2c794292dd2e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib/src/multi_lock.dart", "hash": "2ac6fe0e9a4d7b15855dabd7468cc320"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "3cd5a71cfa881a4d3d6325d6b2c6d902"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_transitions.dart", "hash": "709e5921e8c605c3418942ca3def0869"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_c.dart", "hash": "7f1486a2bf169b977f3be1524f930a6e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "107c33a245427bf0f05e21c250653dc6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "a340eddbf129cfd60e2c67db33c6003e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "1603f38e802a78686ee48e3554da22f8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "9ec81b597c30280806033b70e953b14c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/cssom.dart", "hash": "fe51ff1e9287f5f07d9e0c75a95ce011"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/md4.dart", "hash": "3c45979b68b6aa985418f9a03a5b79ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webcodecs_vp9_codec_registration.dart", "hash": "fbc14c398e33c1635b85a027d1b1bf51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_color_buffer_half_float.dart", "hash": "74bc91ac0e2a797930d6f45776b0915c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/cookie.dart", "hash": "1b3a108057faeb078cf3ff9853b92511"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/unparsed_frame.dart", "hash": "0c30a117b0d1fd5c94980510832b81d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/utils.dart", "hash": "ebf21341320c02b09bfd8dcbfc683398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/content_blocker_trigger_resource_type.dart", "hash": "76d5ca39fe194f77a386358f3ad60333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_storage/ios/web_storage_manager.dart", "hash": "afb8111acfc5409669fd7cde43da525f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/scrollview_deceleration_rate.g.dart", "hash": "0e197d116de3de73742123866cfffc74"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/daily_motivational_quote.dart", "hash": "7d85d23a7b766792c1e7006915a37840"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_stream_cipher.dart", "hash": "595e228e65be42abc3a612b741bb541d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "ee36aadc3fac54d5659c94c6aadcd007"}, {"path": "/Users/<USER>/Desktop/66/lib/services/network_resilience_service.dart", "hash": "06894d61dab23913af6d6033f8dfa88a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image.dart", "hash": "e03a984efe74a058d3393aba7c55fe1f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "2baf11d03f1f50ccef5294c1fe810e25"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "a89f6417642d57961ee87743be4a6a2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tree_base.dart", "hash": "61b8716847e9a3ca1bff526d7603b9a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/user_preferred_content_mode.dart", "hash": "5dfefd6697d7a45c2764f2ae32a402dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/mixed_content_mode.g.dart", "hash": "3265fad8360bc6bc568ac9e9502092d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_localhost_server.dart", "hash": "1651683d3e561bbbe53c2e0263ae279f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/user_script.g.dart", "hash": "66686e92944187135287056d97f33408"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "hash": "bb500500256905950683ee38c95fb238"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "1b3814e3cd3f2d9543c7ebaf88384e10"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "62b4a318d3ec0d03d3dc78b84cf0458a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "8a39bdc324d0ff25097784bd98333c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_l.dart", "hash": "90a6d35e7a7db7adff31af7c8aeb6182"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "64a2ea17e8058aec30096102af030f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/base.dart", "hash": "86039b13313ad468f867bb5522411241"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/material.dart", "hash": "ff1b06a4c51e36902ef2e5cf96495fea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/x501/asn1_rdn.dart", "hash": "4c085993923bdb9c4040d1ae74c6a280"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/generic_sensor.dart", "hash": "589d6d019d54515cce02c54dc2532c8a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/helpers/http.dart", "hash": "85eb2b5d0e8262c6ff2a3f28b63538d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/trusted_types.dart", "hash": "492de3051f108aac26fbbf7f15f2dc62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/digital_identities.dart", "hash": "8ffb32766ef04667cdf8767229bf2696"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "build/web/version.json", "hash": "540af34ff6af6be1b03b295bf94bd645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/Formatter.dart", "hash": "35054401ba5ecdc8134dfd5dc1e09f10"}, {"path": "/Users/<USER>/Desktop/66/assets/images/logo.webp", "hash": "fabcd654c28a0e5216ae3a8093046520"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/extensions/rrect_extension.dart", "hash": "bd6edf459ed2affde49bfdedff60fe42"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/keccak.dart", "hash": "364420045b806c9db17c9d10e91aed8f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "cb745b78bdb964c02c1c4a843b9c1e7d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "4f9995e04ebf5827d1352afca6adda26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/touch_events.dart", "hash": "99587cf948b50333494149c8effe0d3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/android_intent_plus-4.0.3/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "db24bbb74875ecb216e8445bc10a0714"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/notification_privacy.dart", "hash": "7e6cadbf7dc8d6336608147477a67804"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "a0816d2682f6a93a6bf602f6be7cebe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/helper_methods.dart", "hash": "0d0350902fa7b7c829baf0666f1a74dd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "e461dc9f79fcf6a9e4faf12c8182fb47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime192v1.dart", "hash": "78f28534085a9036071a8642c131a8cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/base_chart/base_chart_data.dart", "hash": "617cefe4fbc2fbbfe88f461e4f59e022"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "0e3d746a279b7f41114247b80c34e841"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/trusted_web_activity_display_mode.dart", "hash": "7c27f5b834e62344726b6b8df6522096"}, {"path": "/Users/<USER>/Desktop/66/.dart_tool/flutter_build/d12e46422de5e2738ca11434eca92746/main.dart.js", "hash": "8e80473078df96b0d995beab956af266"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/src/interop/utils/utils.dart", "hash": "fb14d876dbe0ddb29976a8f6ae222c7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/utils/path_drawing/dash_path.dart", "hash": "1f0c168015d074facbb8fa7075ee3e91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/platform.dart", "hash": "b92ed901e8df2fde6d4739ed5e59051d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/concat_kdf.dart", "hash": "2c9cf8b4e1efe153fd93f70e73ed3e9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_cookie_manager.dart", "hash": "6e5512b9bc545c165054190477d80776"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/placeholders.dart", "hash": "4ccaab1f2ffd61fd5998a2fe8a9be886"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/gctr.dart", "hash": "31e1c73ec1091931223281021493c8b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/signers/rsa_signer.dart", "hash": "f3c830efdb17a1726ac1af2ec003ef61"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "03001d3ddae80bbf1f35c5e70e0d93e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/types.dart", "hash": "83bb9dfd0d336db35e2f8d73c2bdda85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/tiger.dart", "hash": "2a2fe8fec0bba8749eb1a1a4a8f16e7c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "eb89408ce23b2abcd324ea5afb05a1ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/char_code.dart", "hash": "4fb96b9e2073cadc554a25b36f55e6dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/pull_to_refresh/main.dart", "hash": "66f9f6997e6ee517d6fe5549003879c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "build/web/splash/img/light-1x.png", "hash": "4528f746fdcaa895355939d70cf75306"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/LICENSE", "hash": "ef2f4f5048c86bfd71a39175b6f103d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/firebase_exception.dart", "hash": "7cb7fe22378ec39b40d4b519d0928d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_protection_space_proxy_type.g.dart", "hash": "2b6f5e2182b79f7d9be1447aa8730311"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_bit_string.dart", "hash": "4504c6fed6df7d55aa56cf1aee1a1676"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "0d1b13fd16692571d5725f164d0964ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1ia5string.dart", "hash": "ff177b23419bdd4e25448562c040a8c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/mime_multipart_transformer.dart", "hash": "531d1d96bce7aa59a6109c02ac538cb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/console_message.dart", "hash": "70054c873642f7209594be5b6adc8ec7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/website_data_type.dart", "hash": "56136c6ba26944d6230d5cdbb5feadf0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_response.g.dart", "hash": "ce4ae7ab1c450b98860be5dc714d9811"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/script_html_tag_attributes.g.dart", "hash": "da430abe4b0bfcbcde2c7ce1502e3a4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/ripemd128.dart", "hash": "77233d53e3db6b1f94881a6e822dd168"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/argon2.dart", "hash": "c048aa4f98f732449005fdd5f36d98ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.0/LICENSE", "hash": "95fbbb6a31fd950ed3b6f95caee42345"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_storage/android/web_storage_manager.dart", "hash": "3028c187b59e09a1671003dd56928b3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_internal.dart", "hash": "2e45d6380f8c16c4eba3ea6fc3ceed39"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "4d8781c671b7df5aadf2331931458cfb"}, {"path": "/Users/<USER>/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "ebc6759fa73c53bc12d581d9e1e4c821"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_protection_space_proxy_type.dart", "hash": "c86adf56af836fdf6f3bd5aac60feaa9"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/progress_page.dart", "hash": "151b03a3d0699039aec716241099103d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_webview_feature.dart", "hash": "a746b357b0dcd0b5fcc4246925bd0bf9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/content_blocker_trigger_load_context.g.dart", "hash": "4a83d03bf2e835401bcb9810627c8333"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/course_settings_page.dart", "hash": "3f66218be18a2417545dbbdd9ac222df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "hash": "5ffb77551727a0b5c646196e7bf1e9bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/favicon.g.dart", "hash": "63d4b6fd6726ccd470862919aa783471"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "0e708f7885d57fccc31cdb5020c2d9c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/ecc_fp.dart", "hash": "5c01f919dddeedfac753d5311b6c7f01"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "7c4df8be3ef1b8c4564f6aa3c64ba65d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "7bd8137185bc07516a1869d2065efe0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_authentication_session_error.g.dart", "hash": "da44785ba21c7222ec947b26904237c8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "1338341fe43eb21f20857cc392cf2f71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/lib/device_info_plus.dart", "hash": "fee0c768d8067a192b2bfb5c110b243e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/sequence_equal.dart", "hash": "e9e452fa340b489a49dba00eabefa3ba"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "e5a3ca065f292c0f0b0cca0a55df41aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl1.dart", "hash": "1127949efc41840c01de5f126e84bcfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sql_command.dart", "hash": "4e7b4cf98b7ea45960f7d79fffac5705"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/lib/src/sub_rip.dart", "hash": "494f83f832ea9b2663e3b56fb49ad5ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_prompt_request.g.dart", "hash": "acb843c7fac251e1f789f16992aeb81c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/client_cert_response_action.dart", "hash": "e709ebb3caeac6050da58a408696c137"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webidl.dart", "hash": "e277cd24cc460f69f51b0256a4f283ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.56/lib/src/js_interop.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/gostr3410_2001_cryptopro_xcha.dart", "hash": "ebd5f3f78c5f54bee9f7bdf83de334ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/helpers/events/streams.dart", "hash": "5d85e68dab1c562040338e8166c9e6b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/picture_in_picture.dart", "hash": "ccc4239831a5ea14583942ebea81a7a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/src/cookie_manager.dart", "hash": "ccf33e80ffacb6295aa8ee093d0cd561"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.0/lib/firebase_analytics_platform_interface.dart", "hash": "5ab59f9d82cfcfb25d05fffcfca43bed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_block_cipher.dart", "hash": "f0ba8d1c1549b2073eca13fa44f5930c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_counter_styles.dart", "hash": "8bc41708c1ce9560925bd8a19a92d8e9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "2627dee7fb363a5bb1cbc919699bcc84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/case_insensitive_map.dart", "hash": "5893c7d3910e8924bd2dccc8837775c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_printable_string.dart", "hash": "f021f69fef1a6d0b8a8870757b9023fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_srgb.dart", "hash": "260defa43d3ab6d805cffffbd379859a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/utils/assert_utils.dart", "hash": "ebf1a836264dd7e40b4b3a5044e4a36e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/bar_chart/bar_chart_painter.dart", "hash": "5caf679668a3eaa520db341bbfbca6a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/x501/asn1_name.dart", "hash": "80e54c212b5553089f84e0239fb6c570"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/pie_chart/pie_chart.dart", "hash": "3e1adca9705ce7c856327d9e0e55e46f"}, {"path": "/Users/<USER>/Desktop/66/lib/services/auth_service.dart", "hash": "a564fc01b751499ba6db99089831217b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_multi_draw.dart", "hash": "073065873f7133a121a3e2995f6377db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/polyfill.dart", "hash": "bc0eb13caa9c0425831f18962dfe12ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.0/lib/src/method_channel/utils/exception.dart", "hash": "4d220d6cb85d3521eb773091b5c21b29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_null.dart", "hash": "44b633ff9265431f3dbf4aa9d0b5009b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/base.dart", "hash": "8e16702463aaa9f1da9da189aabae66c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/web/js_bridge.dart", "hash": "f2d661b4a9ea709ee1beb94d41eac1e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/helpers.dart", "hash": "0f34791090b23294972bf4a7010dc726"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/fonts/fa-solid-900.ttf", "hash": "43d81179d8a87ad8533c6563c72158f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/navigation_action.g.dart", "hash": "deb6d5cd4bfe9d208bc8d435d405b2ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common.dart", "hash": "1ab2b4b439160093cb35c9b0c739bc0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/platform_check/platform_check.dart", "hash": "c5036d9517a6d4cf0bcd2a978c85df6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/utils/resource_image_provider.dart", "hash": "6de1ab9dce0f03de43decce8e25e3637"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "feacc941aea1ec8b3a30601915b7d353"}, {"path": "/Users/<USER>/Desktop/66/lib/utils/json_utils.dart", "hash": "0dc12c1cfe89d6d1f912d574186f4610"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/webview_environment/webview_environment.dart", "hash": "dd5a50651ed62ffbc1464f55f736e88a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/rng.dart", "hash": "d42791632fba8e51a8bc7535cee2d397"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "2de077d432c4bb0a9525e9ab5d84913a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/in_app_webview_rect.dart", "hash": "b9a22194fa3d74cbc3debec6ca8b8f89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs7/asn1_content_info.dart", "hash": "145a8a4316985af89a3dc6736ed3178f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "2906cf9308cbed8eb54ab1638dd5f56e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/dom_parsing.dart", "hash": "341172e2f74267b9345cb7cecfd16d2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/LICENSE", "hash": "cc58abec61a28a7ec53b4606f76d07fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_message/main.dart", "hash": "9a59f5dd23a4f77f980278de44b48a71"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "a732cf9cb336d70db5c1145f0e468953"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "45beeaf92542183f39c458a87dcc81f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_storage/web_storage_item.g.dart", "hash": "ee518d62aa1f9c6ffdb44c9a8f174374"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "9298606a388e3adb5f1bbe88ae45b1e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/method_channel_url_launcher.dart", "hash": "351ed98071b53d3c2e98d376f2a65a74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/extensions/extension_navigator_state.dart", "hash": "6983e9bac62595b7e8135be88eead182"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_directory.dart", "hash": "96ef4798e4cf4560148762dd71bd180a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_options.dart", "hash": "5f44f436ff7b1129b18a489faab45005"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/flutter_logo.dart", "hash": "985cf5499dc6e521191985f55245a22c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/collection_extensions.dart", "hash": "874c21db82e74ec1d570b48ffb1bad17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/utils/list_wrapper.dart", "hash": "6a01e1ffc394b73c4ddfa94728fe9c96"}, {"path": "/Users/<USER>/Desktop/66/lib/services/auth_interceptor.dart", "hash": "436949c7f6df623837a032417be7fd93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/padding.dart", "hash": "c9133480cc3efe535d32fe91abaadc6d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/LICENSE", "hash": "53d71cd0b0021b18407a0b37287e730f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "c2e0fa3415ed461288b6e2aecf569919"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/in_app_webview/in_app_webview_controller.dart", "hash": "41df75ef24ed9d386ac5fdd70b049c9c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "eafb3b31ec7cebf556a529810d6f649a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/private_key.dart", "hash": "043e8bef19eb143ecd9d3db40a080b5d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "a32174b6de983c1652638940e75aae6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/registry_factory_exception.dart", "hash": "a9f8eec89e7e84de670561aa40b57d66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/date_builder.dart", "hash": "bc1f35bad7b3fd785bd8734292b27ff7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/_isolates_web.dart", "hash": "a8986df0b5d73e87801a54e4db6a9494"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/process_global_config.dart", "hash": "23296c89380af5dc34ff1aa492fe1d11"}, {"path": "/Users/<USER>/Desktop/66/lib/utils/platform_storage_io.dart", "hash": "807bbcd1da1effab111932cc5ded14fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_output_type.dart", "hash": "f9ee95528951098ddf3e1d5305e65393"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "ed582bff49cac60fb08ccee9ccc7c573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/chrome_safari_action_button.dart", "hash": "302e3ceb487d7c9d30879b5a79653247"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "f7b9c7a2d1589badb0b796029090d0d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/permission_response.g.dart", "hash": "c79c3dd0eadf4fd368c6aabbdcb8276c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/cross_origin.g.dart", "hash": "f87f8f4042a6a5732839719609c66fdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_i.dart", "hash": "706f1120f2aad4e908056a2b4f16eb23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/utilities.dart", "hash": "c18ab890f45960c7227edee678cbdf70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/adapters/stream_cipher_as_block_cipher.dart", "hash": "e9c8e087c6a13fa13c36504a1228859b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "3de98898d0fea89f0e609dcbf7b69783"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/ct.dart", "hash": "dfe75b0d095b9d0520e61e45b3a19be1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "hash": "7cd08032583ab0a8eca895b2365a4583"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "85814d14dae3bc1d159edd0a4bef48e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/key_generator_parameters.dart", "hash": "c7f97dafe25263ad7381f4b00c2ec24e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/fetch_request_action.dart", "hash": "525307bc55fbb8d8d8fcde111f6fdfad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_proxy_controller.dart", "hash": "6b8803217321520f519df2477fe190c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/rxdart.dart", "hash": "6d2dba952020d690bfc0aaff3adbcd65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/vertical_scrollbar_position.g.dart", "hash": "78e12d4e6c8afd567ed4cce7df1f4241"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/parameters_with_salt_configuration.dart", "hash": "59fad971cfc70efd0d2fe9df45863abe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/switch_latest.dart", "hash": "a52ae2e097914c25b04abb01abf02183"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image_handler.dart", "hash": "a9ad1aa35c1b9117f15a379ef03480dd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "cf5dc26d65244c12416f3411c6d79996"}, {"path": "/Users/<USER>/Desktop/66/lib/models/user_profile.dart", "hash": "fc2a746089104e02cb170a07a3e7a3c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/permission_request.g.dart", "hash": "a7c51167ae0eae964835a8fea0760563"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/firebase_core_platform_interface.dart", "hash": "a12b9a0771829ebdd5571928f9c48e7d"}, {"path": "build/web/canvaskit/chromium/canvaskit.js.symbols", "hash": "5a23598a2a8efd18ec3b60de5d28af8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "b8c09bf358fcebf2f4c9214d1007536d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "438f80a3d5361329aa6113e3409440aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib/src/exchangeable_object.dart", "hash": "60563ee394b1892e35c200031e6fd771"}, {"path": "build/web/splash/img/dark-1x.png", "hash": "4528f746fdcaa895355939d70cf75306"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/extensions/fl_border_data_extension.dart", "hash": "93bdfb7e39afdcf0b595d826ec6cf60b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/streams.dart", "hash": "08ebb996469240d7789e7d2ba9f08bc0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/modal_presentation_style.g.dart", "hash": "b342f18e4e496689ec876e35db54f91a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/range.dart", "hash": "57de88edbfb0d8951419051441a6de56"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "9a67635cfd2e047d996c4840d4cb18ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/aead_parameters.dart", "hash": "e3eb86ef252b151d52688c8904935b3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/authentication_challenge.dart", "hash": "395f07418a28b12b0ed665f32270d702"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE", "hash": "c458aafc65e8993663c76f96f54c51bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ajax_request_event.g.dart", "hash": "9a1799cc050615a5477f200290e498fd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "dd3402d5403be91584a0203364565b1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tokenizer_base.dart", "hash": "e3bb2a25791065817d184fabfb8f7d0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/node.dart", "hash": "2f9772d14db922d3a41fb27f6b6382fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/ecdh_kdf.dart", "hash": "d23f9307b748f3d399fa8578e9fdbd66"}, {"path": "/Users/<USER>/Desktop/66/web/favicon.png", "hash": "969c51f58546ac38d88e3bf319cccd66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_rendering_quality.g.dart", "hash": "559013aec6149ab0d8a71d1af4e6588f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/custom_scheme_registration.g.dart", "hash": "31bb3fe1dfe7c14f94b040e4c813f60d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1sequence.dart", "hash": "891b2d9b6bf04296e789fb20012e5908"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/utils/html_utils.dart", "hash": "6159e4d99d3c488930b4d45c61f4a63a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_properties_values_api.dart", "hash": "220c3732a923196f9a41b6c327dc3fe4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/layout_in_display_cutout_mode.g.dart", "hash": "5d0acce3bd2503d87b6b81b719053801"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_confirm_request.dart", "hash": "a71899138cb7b4ff136926d48ccc4346"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "056355e344c26558a3591f2f8574e4e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs7/asn1_encrypted_content_info.dart", "hash": "d4188a33d3cf0948a179a12f37c5a0c9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "5b66a624b831dd9a7c94d59aaa10f8bb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "5aa32c5e6b696b66556b4f91bf5983a3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/mediasession.dart", "hash": "8a27b04fdcf4b9f1024072549363b25e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime239v1.dart", "hash": "3ababf685f66cd8d5b13ed724d607809"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_confirm_response.dart", "hash": "817d1b4aaf147366e73c15ff5b6d376b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_service_worker_controller.dart", "hash": "c51daf9c1cd84e3e2bf16e8c5fdb5662"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib/src/exchangeable_enum_custom_value.dart", "hash": "4eb673764423c24a0514c95c18ffa1d4"}, {"path": "/Users/<USER>/Desktop/66/lib/models/motivational_quote.dart", "hash": "c54566022e47d86aa4a0a997eeb09827"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/src/package_info_plus_web.dart", "hash": "b4ea9ca5298e97e67aa49b8d6408f286"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/asn1_object.dart", "hash": "bc18934a7b1a4820c999a7df4196e282"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "dd518cb667f5a97b3456d53571512bba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/scrollbar_style.g.dart", "hash": "24a85ab4bae1ccaa80f45c8db5e5d848"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp384r1.dart", "hash": "88aa7805c269dbc20cf16073f7945e46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/x509_certificate/asn1_der_encoder.dart", "hash": "204e58aa979132664fc96eba894ebfe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/number_symbols_data.dart", "hash": "5ed0f2083353eabc56bf4593cb10bff7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "52d0e96cbfe8e9c66aa40999df84bfa9"}, {"path": "build/web/assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf", "hash": "3ca5dc7621921b901d513cc1ce23788c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sqlite_api.dart", "hash": "9442b7f8efe89c42cd235c4047480ce4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vimeo_video_player-1.0.1/LICENSE", "hash": "f38e50136be86858ce26c7441e0bab0f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "203fbbac922589879ae44083b04a368b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/mst_content_hint.dart", "hash": "2df5e106795b5fd5f73cc1505132b57f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/helpers/bitmap_helper.dart", "hash": "7e41cd81c3e1b5b24dfb968d4d42f28e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/family.dart", "hash": "9dcc50108fd667c7744d5bba6b51e1b4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "9d6f9dd391f828bccdbb47c5072c04c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_conditional_5.dart", "hash": "6e9e644f0613d2701339b82c7dbe6f4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_storage/web_storage_item.dart", "hash": "5e171bfd8fe92e64665828c71f4244a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_t.dart", "hash": "8f142b64056bff3425661bf170728f45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "c3ccb5b6cd3df44e6587a4f04dd6a4e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/platform_navigation_delegate.dart", "hash": "ac62a3b87b64443265423b4ba48611f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/client_cert_challenge.dart", "hash": "0c9519aeeafbae0bdf8e88eda9e5cc1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/paint_timing.dart", "hash": "4c622e5476419d4783b3367af90e04a0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "625b858bd9847eab75d2f3f6295a25bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/unsupported_asn1_tag_exception.dart", "hash": "b699077c09fbd1eef398dd92fe7f2d03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/web_options.dart", "hash": "7dff3a0a1b5652f08f2267907c79844e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/common.dart", "hash": "33f949ceca0aa8895b2fa0ae289f42d0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "f49291d1bc73b109df4c162db10003d2"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/pin_input_widget.dart", "hash": "ec3b163511f635f25ef8f5e2d6cc76b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/render_process_gone_detail.dart", "hash": "6c2e1ceed8c97a27c45e63d1a44a550e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "3f7c50b425818ea563c8459cfd6f9d5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/uievents.dart", "hash": "8b3fe6eb34b48a71f0c3e444fa83e5fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/webview_package_info.g.dart", "hash": "2f0fa8d5ca718adc31354212d923f6ed"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "56a764067b45a1a7cb6b7f186f54e43a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "5fe5b5ed3ec92338a01f24258b6070a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/webview_cookie.dart", "hash": "667426285c5e989d91d20d4cb9ac4424"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/tokenizer.dart", "hash": "a8e51be045a7977648c023a4531317f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/lib/src/model/linux_device_info.dart", "hash": "f4891497aafa39d33f3b561e761b7b9a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "ffc66c213d3e015ff3e03298622c7141"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "ecc072620f2a72e685360292690c8a68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1bitstring.dart", "hash": "06b2ae65860a56ae73787c243966a98a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_error.dart", "hash": "47cb151906114ae0161078bb7968ffc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_k.dart", "hash": "de8b58c147e392ac3e1a5479f4941290"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_http_auth_credentials_database.dart", "hash": "470edab7e491ef396377a6ff7b19b985"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/list_proxy.dart", "hash": "d1c07a46157914ec4aaa9aa9a957df37"}, {"path": "/Users/<USER>/flutter/packages/flutter_web_plugins/lib/src/plugin_registry.dart", "hash": "41322b445cd296e75b2d2e6df6cfd62f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/server_trust_auth_response.dart", "hash": "9bb990d74a1fc4c69251dd602a185f6d"}, {"path": "/Users/<USER>/Desktop/66/lib/services/navigation_service.dart", "hash": "f1781249d81938d99200af7bc9f1b97e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/attribution_reporting_api.dart", "hash": "5001aaa956012cf3be30b4f1c7cf9efe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/print_job/print_job_settings.g.dart", "hash": "8710713150e881892960f2c0787420f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/intersection_observer.dart", "hash": "819fcc538d96464923b4d6c08b2bec29"}, {"path": "/Users/<USER>/Desktop/66/lib/firebase_options.dart", "hash": "70a5beeeefa75cf5d10dc1a28dceeb14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sql.dart", "hash": "597e7b293e2531edc3ef788375e11c67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/proxy_scheme_filter.dart", "hash": "60f5622c832c18380d36c0a1883da75e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_response.dart", "hash": "8a4e81e8fccc01dc69bbc847b75a31b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "4591f6273e6282466c0364d5331e50c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid.dart", "hash": "ebddd1b3c6af3141a7d2025fadf56ada"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/src/fa_icon.dart", "hash": "54869383f8b9e52cb77cfe1211a8eed6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "ced9d2439e23015bfc2bac438f598985"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/utils.dart", "hash": "2d3b2846d7071fb93d36485c261040ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/lazy_trace.dart", "hash": "d75954340a0c7770eb9a149f7994598e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "85e90b0b1f705d7db10d294017bcaf44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_alert_response.g.dart", "hash": "b1340efa42d750b17f82a1cf4bfc6bad"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/web.dart", "hash": "c6ae9d71557165d4f4822bd8545dfe60"}, {"path": "build/web/flutter_service_worker.js", "hash": "f36786772476ee14f7b5dc09d9437ea1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_safe_contents.dart", "hash": "cd768a314319e35ac7f3d960700c7aba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_media_size.g.dart", "hash": "8f70c406dd3cd8134e786ad3d324499f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1application.dart", "hash": "10e51c3546a0a1a57a4e54478af9514e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/trusted_web_activity_default_display_mode.dart", "hash": "306fb1b15483f80e814d385d3ddceeee"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "cd6b036d4e6b746161846a50d182c0b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/x509_certificate.dart", "hash": "f234000ceb9d457fa80993e30f7aa769"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "94235ba74c3f3ad26e22c4b40538ce07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/rx.dart", "hash": "f222f3be7d9e176a7d8ba3252825c9f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier.dart", "hash": "a67d1346ef152a92e983a9d7dc1a96fb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "4eede9144b4c0e4b14bd426654183174"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/optimized_image_widget.dart", "hash": "dcf03148c1f837a394a0b8f11173d0d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/tokenizer.dart", "hash": "80b8464423b79136f9fc5a427a1dafb4"}, {"path": "/Users/<USER>/Desktop/66/web/splash/img/dark-4x.png", "hash": "7520625a040b816f2d8ec86ef8b5af52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_color_mode.g.dart", "hash": "703ab9ec37933187196fe12d7f7d0e91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp320t1.dart", "hash": "4f64dcf9751c389b5608c9247ae89a91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/lib/share_plus_platform_interface.dart", "hash": "7e38424729d139f4ac1ff5183cd59303"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/custom_tabs_navigation_event_type.dart", "hash": "6ceaf440d812445f4e2e8313987b9b0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ajax_request_event_type.g.dart", "hash": "d946cd390aee72692f4b4968ed2091db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/helpers/enums.dart", "hash": "b6cfd47ac7d8e231ddfcacefa676b749"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "02dabe6a8cd832d69b4864626329ef30"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "98cd866294c42f2faff3451e5ca74bfa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_request_network_service_type.g.dart", "hash": "ae502347bff4b9d947d2ebbfd7369f99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/lib/src/navigation_delegate.dart", "hash": "8d73319e81164cdc6df47bd20d4e731b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/custom_tabs_post_message_result_type.dart", "hash": "f0df2f42598dc3b4ac81197704dc8a09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/client_cert_challenge.g.dart", "hash": "0b7ecf8dd00c3c026b94dfe49e5496a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/interval.dart", "hash": "b2a2c73b0b7b528180181e9e4e3b4e92"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "2aec07fe4a1cd25aa500e5e22f365800"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha256.dart", "hash": "1ab8b7099e1db649e9b7337d005dc3fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/http_auth_request.dart", "hash": "8b747b3e31a6d6f2304004353211fb31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_integer.dart", "hash": "3d8afe52b6baa8d2f4a8209746f41ba3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/operations.dart", "hash": "5dbef5156368d0f25b59750608e025a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_message/platform_web_message_port.dart", "hash": "a1034a47fb1d33afdf69bc382da31f9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/pbe_parameters_generator.dart", "hash": "a93d576823e58041b99309fd23a1d547"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/proxy_provider_listenable.dart", "hash": "2e59aadb17c005953c2accd529aced98"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "c86a43bc5abf7528416982490b4c0b8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib/state_notifier.dart", "hash": "5bc3c944f62b4cf5d382a0c0e9b7e09e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_output_type.g.dart", "hash": "e218eba2853da82dcd6a946bbc6fe2c0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "0a731b52181a917a08ac96b525f7d96b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "6486bc074c81ec57bdafc82e6a64683a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_plugin.dart", "hash": "ae3622db94fb8368f3577f6e71f3ea4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime239v2.dart", "hash": "5c05c15d14b799cb005474b4676d8fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/compress_format.g.dart", "hash": "f37f6471fe06426ef7854f34934d0afd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_mixin.dart", "hash": "0f5d8dd74761633229f5cf2fd6358e05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/chewie-1.11.3/LICENSE", "hash": "44d369d298afe1dc89efefde03ae7220"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/src/firebase_sdk_version.dart", "hash": "8871c08983b2bcb0df70b3c3398f161f"}, {"path": "build/web/canvaskit/skwasm_st.js.symbols", "hash": "c7e7aac7cd8b612defd62b43e3050bdd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/performance_timeline.dart", "hash": "3ee923a2e66258d09bacdd2223e9dc29"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "1f131d7f971396d52ce5fe78ae6a8a83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/lib/webview_flutter.dart", "hash": "35c1bdbce56db473ac5ee45089c934be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/referrer_policy.g.dart", "hash": "a1cde830915c78ff60c6a0b5e6f72922"}, {"path": "/Users/<USER>/Desktop/66/lib/config/network_config.dart", "hash": "360ef580f4d416b1599753860ef4c184"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/cache_mode.dart", "hash": "e2c7e7c82ffbbac46141c9bf72c95522"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/website_data_record.dart", "hash": "401847897b394906f8967f7ee4d7143f"}, {"path": "/Users/<USER>/Desktop/66/lib/models/course_video.dart", "hash": "c422b716991e94cc622d0ad4df269550"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider.dart", "hash": "edc6185b4e4994b45acda6675696d87b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_aead_cipher.dart", "hash": "839c5b0bd0d69f5a9b2875f391a1bc9c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/lib/src/share_plus_web.dart", "hash": "8c2447d5dd0dc7741e403fb6e607287e"}, {"path": "build/web/canvaskit/skwasm.js.symbols", "hash": "80806576fa1056b43dd6d0b445b4b6f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib/src/supported_platforms.dart", "hash": "26c47fdd4bb000b6b794163e74974e1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/pie_chart/pie_chart_painter.dart", "hash": "30b4ccc067e519b1faf020d442a74ff1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_duplex_mode.g.dart", "hash": "eb7668b56e4d13461247b4345c402eae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib/src/subtle.dart", "hash": "290d88a8e691d6be881bfc35e83b2bbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/multi_image_stream_completer.dart", "hash": "4870aa3bcaa04ecc633da01dbd2c9560"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/health_metrics_card.dart", "hash": "ba6f618c6c1ed6ea676d1a8ea8b328ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/foundation.dart", "hash": "f594087d1804ddc538f758c0059eb6da"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "dc552952c58db02409090792aeebbdd8"}, {"path": "/Users/<USER>/Desktop/66/lib/utils/app_assets.dart", "hash": "dfe6d62c189798f0a683dc2376110ab0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/permission_response_action.dart", "hash": "28bfce23631549cb64cd891aad362115"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/auto_dispose.dart", "hash": "39d249bfedd0655b147701ff81de4fa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/pointerevents.dart", "hash": "81f93ab4890d03a269bf7927aa31cd7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/screen_orientation.dart", "hash": "4fdc43d22013e6a2f9c8e301e80c7096"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "5f64d37da991459694bce5c39f474e5f"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/hydrated_progress_widgets.dart", "hash": "29ee6be13963c9cfd8944a38c3058878"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/javascript_handler_callback.dart", "hash": "84bb2ba732fa2fd862b8f978cae6bdd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/i_awesome_notifications.dart", "hash": "6fd54b57ab7ef96f8cb166688b020789"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/LICENSE", "hash": "53d71cd0b0021b18407a0b37287e730f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_storage/main.dart", "hash": "6c93798244a3dddf65360d193bf745e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/batch.dart", "hash": "d88008fc349dd84def0654263c6d16be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/algorithms/salsa20.dart", "hash": "7b2925b690ec9bfbb94a651070da76f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/scrollview_content_inset_adjustment_behavior.g.dart", "hash": "ca9ecc7d8ceb6e678789f481b0a76ca9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "e556497953d1ee6cd5d7058d92d4e052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ssl_certificate.g.dart", "hash": "f4b3e30e108655485d9a37a45d02988f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp256t1.dart", "hash": "545a2816c908e3ed8595fa9d883cfe89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgpu.dart", "hash": "bfaf083479abcc6fad1aac4531783dcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/srp_client.dart", "hash": "092fa998bb70518958eb737eda80dace"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with_many.dart", "hash": "902509bf876a10a7b6e534a1d24fb476"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/plural_rules.dart", "hash": "4b43d777bb553eecd35ca72e6d99ac3d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "62f6d0411965eefd191db935e6594f90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/asn1_parser.dart", "hash": "d75b4941ea5e46e3ecb8ce8f22847817"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ssl_error.g.dart", "hash": "8bf66714805bdec914467276b9b49377"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_store.dart", "hash": "b72b9cd4de477e80296c7f58bc9f5f30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "0201ee9c8aee2bb24db2c74b6c0cd485"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/messages.dart", "hash": "31e2179466decb4da4d2ae1e51938a51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/unsupported_asn1_encoding_rule_exception.dart", "hash": "1462f888e54609c461c3d74b7314db6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/aead_block_cipher.dart", "hash": "8ff5264ae4e3db190e31c45ad6f69cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_cascade.dart", "hash": "e3f89d472d6e772b82c5e22a6a8fc60d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_r.dart", "hash": "c03845abf8fa02fedbc602853685d92a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1printablestring.dart", "hash": "a083d071461a2f6eb2effab7f2c1c2d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/query_selector.dart", "hash": "072bc29df9af18240c9691c60edcc988"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_paint_api.dart", "hash": "79e2191a8641bdd80f9ff0de82ff35a2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "eb9b3bf513b18ddaf0057f3877439d9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/factory_impl.dart", "hash": "65614758273c0b82b4ce22b3728be36c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_boolean.dart", "hash": "922fed15740c84a21712faf7a96206d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/defer.dart", "hash": "d06420fd88fb8f7cc3acc1643051178a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_tracing_controller.g.dart", "hash": "ae87d3128190486959f8497749f84679"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/key_parameter.dart", "hash": "efffd75c3a5f665f679d9773bb0e4695"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "be0a77cf3f0463f3dacd09ec596d9002"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "b24af65afbe06cb00d5661df3d3083af"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "07664903d8026f2514b29b786a27f318"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_debug_shaders.dart", "hash": "80e323d4c1ed63a9ca4160e52fb5a98c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sqflite.dart", "hash": "f7be2d6ca06ea6e4deeee0e441b71d6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_disposition.dart", "hash": "561e4b5dad82ce30a09b7667451614ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/reporting.dart", "hash": "41097783dd4318deeac7be3e96677833"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/number_symbols.dart", "hash": "aac4f5ac61e2386363583c54f2e49a7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/_config_web.dart", "hash": "efa004cfe7e84b938c00c9a03105c34d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/bar_chart/bar_chart_renderer.dart", "hash": "ad8b05ec18ed836fee21308aaf371841"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/aead_cipher.dart", "hash": "3cfbf0fce1ba4d19780f691ac1ceff76"}, {"path": "/Users/<USER>/Desktop/66/lib/services/course_tracking_service.dart", "hash": "35e26b1b7004fbb4dd7649ddf241038f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_browser/android/main.dart", "hash": "c448e177ea9bee0de9975083eee6ab66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp512t1.dart", "hash": "79edf39a6bc9b256fa40cf91ac34c08f"}, {"path": "/Users/<USER>/Desktop/66/lib/special_category_workouts_page.dart", "hash": "853423f7af06a9ec825e4f6836a71092"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/random/fortuna_random.dart", "hash": "db1dccf6cafeea98e1986f83b2978be7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/css_printer.dart", "hash": "9a6fff298db26d4e059ebb664863ab18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_j.dart", "hash": "998746037e3416b31d33881bf69a4148"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1.dart", "hash": "b6c036b7b0da523994ed5a60aaa7ea9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_browser/in_app_browser_menu_item.g.dart", "hash": "24a7e82c667ee6fc53002e07bf4607e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/user_timing.dart", "hash": "2c6f052293c9b2a6f27563e70ec2900c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+13/lib/firebase_analytics_web.dart", "hash": "a63d25272679d24d46992f4692eb3c71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/in_app_webview/in_app_webview.dart", "hash": "d541a9a6d77b879bd8bd06980a6d05b2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "3ecea4d9c25299b0ea66c58256909437"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/notification_android_crontab.dart", "hash": "ddecb6abe6346bfd330c5251edc88ab5"}, {"path": "/Users/<USER>/Desktop/66/lib/utils/platform_storage.dart", "hash": "f9d4e6b768af022d41c415b470c64cdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/in_app_webview_hit_test_result.g.dart", "hash": "c9161b975398e73348ea4d808ea86bbf"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "43ef2382f5e86c859817da872279301e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/rc4_engine.dart", "hash": "bb2f5c45f97f6569c5ff88f1c16eed7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/notifications.dart", "hash": "1ab2ce7d2d7c9d9e510823d8f1982550"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_browser/in_app_browser_menu_item.dart", "hash": "1c5a12c80a364d259585ddc8661c0afc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/geometry.dart", "hash": "1f69b6ff45adef5847a6ab5120852a5e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/_platform_web.dart", "hash": "2aaaa9b2523f4d8471b6584449c10c64"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "94dab76e00a7b1155b15796b87ebe506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1numericstring.dart", "hash": "7f5d429cb9fc83b66071ad938b7e9c19"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "4895dd7c08da98c883cb21943f4ca4d2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "7b71540e417e6ea3f1134b4b677e0624"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system_entity.dart", "hash": "c69896f9c186aab01f7d11624f5c7d4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_f.dart", "hash": "01acde6ab3416626c8fe453d99c13480"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/factory.dart", "hash": "a79e2b9a182eb762fadaab05e9269edc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/x509_certificate/asn1_object.dart", "hash": "8c69952f8ef5a7b04c43aed7c07fca0e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_web.dart", "hash": "9330d5b25f1817c16421ac2f3cde6827"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b5eb2fd4d6d9a2ec6a861fcebc0793d2"}, {"path": "/Users/<USER>/Desktop/66/lib/services/progress_service.dart", "hash": "4854e74c57a3c210545c313a7b1c5c94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file_stub.dart", "hash": "a97e65bfeebec666a235b7c6a4ac0d66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/transaction.dart", "hash": "95701ee376845a2050d29814b7acc7a4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "edbd68eb36df4f06299204439c771edd"}, {"path": "/Users/<USER>/Desktop/66/lib/models/course.dart", "hash": "70e89397b8ec620f0d4ed1a0dee40b06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/notification_content.dart", "hash": "69a885166319f10340fd48d0e7da3732"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sqflite.dart", "hash": "5c96fe82a9bf2dc00db9d93c2c0a41a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/web/main.dart", "hash": "e66b6860ed46f05e0364b34d5391565e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "12143f732513790cd579481704256dcd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "e6f282a4b33b70c7d1d06bec39b155f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_webview/android/in_app_webview_options.dart", "hash": "3f0fcf0e36e6a74209a931fddcd72f61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/replay_subject.dart", "hash": "7929b4d3e79087536edc9cd260f8d4c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/cached_network_image_provider.dart", "hash": "8a55a3a014cc2ba2dea85787efc98ee4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "20d5458a880a0a10253cda660dbc42e5"}, {"path": "/Users/<USER>/Desktop/66/lib/services/user_service.dart", "hash": "a6ba765f4203c483764d77c0d37f8789"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "2c6facdb1b63e687304c4b2852f6ef4c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "dbbc7f46620d816e615bbbe67eb258e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/file_service.dart", "hash": "857464ce6f576c4020591d501ebcaaa7"}, {"path": "build/web/splash/img/light-4x.png", "hash": "7520625a040b816f2d8ec86ef8b5af52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/utils/utils.dart", "hash": "04f2a3236f9f0080d5571041a0cf3567"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/utils/date_utils.dart", "hash": "3735c483137e92b720b4b8d6ae356bc1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+13/LICENSE", "hash": "95fbbb6a31fd950ed3b6f95caee42345"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v1.dart", "hash": "a22d810ba989505f23b6be0562a04911"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_b.dart", "hash": "bf5efe9b7f7e8bdc46aa542818534985"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/compression.dart", "hash": "431a4f8163a783c176877903a4c18025"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/motivational_quote_page.dart", "hash": "41eafb7be0f728a81b7dc850b0eb973a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "7abc7e5212374d29bfe5372de563f53c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/open_options.dart", "hash": "296e60aee7732b001a79f3216058a381"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/listenable.dart", "hash": "a5bfe2d6591e761bf3c5dc0cd4ded99a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/auto_dispose.dart", "hash": "9ab6d0a38467598c8e1f332648cff545"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/base_cache_manager.dart", "hash": "53745062ff0e01e3d763823156d695da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/deferred_inherited_provider.dart", "hash": "59ae3a059b0ba1677002bed66f3b8c2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_orientation.g.dart", "hash": "d410db772f8c80f915cb7895ddeaf5af"}, {"path": "/Users/<USER>/Desktop/66/lib/services/crash_prevention_service.dart", "hash": "77bb6d26d240f36b73f2908bc3700788"}, {"path": "/Users/<USER>/Desktop/66/lib/services/bulletproof_app_manager.dart", "hash": "f1d603f4e44859a830f6ff0e5509e244"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/scatter_chart/scatter_chart.dart", "hash": "9f533782fe4aa9e58044dc6d3557cf2c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "e758d8d6b65597325bd35b5dc769c7a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/storage.dart", "hash": "1c2e53982b49fb3a168b99dad52cf486"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/oes_vertex_array_object.dart", "hash": "aecfb0965bc148911ec391faf91e7417"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "6a7d9ee6c8fae5e9548911da897c6925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/geolocation_permission_show_prompt_response.dart", "hash": "1435382d87c3a048878b061af85b8801"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/src/in_app_webview/in_app_webview.dart", "hash": "d34f1a4cf389503f1458de1c4794d710"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_teletext_string.dart", "hash": "8fb1cb74a8c527ab6dbb3d5ee8a44ab8"}, {"path": "/Users/<USER>/flutter/packages/flutter_web_plugins/lib/src/navigation/utils.dart", "hash": "43841541bd73668ea61f006969d47759"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "a02235e1a98989d6740067da46b4f73d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom.dart", "hash": "ceb8e4633e0ceeb9e91c96c160ca4bf5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "8e043971337ae96a1e56aaf2256540ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha3.dart", "hash": "14f0cde9213a6bba971bace260be637d"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/dedicated_video_player_page.dart", "hash": "e564bfb228fd0bb59a0b27aae3259c0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/client_cert_response.dart", "hash": "16af26e40eee1c890d5858fa740bcf63"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/extensions/edge_insets_extension.dart", "hash": "ee49bdaba1ec44edd11fb9b0d8af5552"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/ufixnum.dart", "hash": "efa0f0bfadfa23e80d42d74b41704530"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/webview_platform.dart", "hash": "477831678f2617c762d84bebf5256f3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/lost_data.dart", "hash": "3bc26601d19fa0f119ec8e7fc5fd6e23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_orientation.dart", "hash": "85aae9b8b5084c0e3bac6a283da3da4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/memory.dart", "hash": "647e49fd7e2b6707e82858420b630c46"}, {"path": "/Users/<USER>/Desktop/66/lib/models/food_item.dart", "hash": "993c7614b2e9094cb1f58c4e38772e90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/lost_data_response.dart", "hash": "064f79178a908761de1a6b8334a36b6f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9c169d41e4740bbc21d0ce33bc753119"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "0cf873bc441372ec89d746477273af13"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "74902317f9caa3ba9c05b114d45d8a02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_texture_norm16.dart", "hash": "a39af050125206166a034535f9fbfd7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_mac.dart", "hash": "3adc21eeb3f32e17731c240849c1eb1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/radar_chart/radar_chart.dart", "hash": "db80c792ed51e08c49da1f17f8c4555f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "build/web/assets/hosted_vimeo_player.html", "hash": "4c49eecaa32beccfa2b0b7801b6779c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/css_link_html_tag_attributes.g.dart", "hash": "9a584ce39e1fa48ad39c2f47bda91077"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1object.dart", "hash": "2454dd0050e99b71559538bb01e04bab"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "2936a409e1029ec52f7c0003f4db18c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_storage_origin.dart", "hash": "93260852a0f5e1f3a54eb279a9b9d0c3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "cc4a516908b08edff4fade47d6945e5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/radar_chart/radar_chart_painter.dart", "hash": "0f5c498535c495a7548d89865b5e9250"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_q.dart", "hash": "e126494233cc791fd4f817e26948cb99"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "5486e2ea9b0b005e5d5295e6c41ad3c2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "9f05403438068337dd8f3433d2757535"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.0/lib/src/analytics_call_options.dart", "hash": "c12403df0d550c9d8b0e626961d60ec1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "177fda15fc10ed4219e7a5573576cd96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_confirm_response_action.g.dart", "hash": "6eb8db8cb407ef233068f4169c8b5930"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webcodecs_hevc_codec_registration.dart", "hash": "1d08fc8c6a5afb14679a1fee86e6e3fb"}, {"path": "/Users/<USER>/Desktop/66/lib/services/connectivity_service.dart", "hash": "95c19d0bb3c7593aeb73462c61d49fbd"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/enhanced_glass_card.dart", "hash": "78d6edcc255bd9475d82dba77be4ebb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "89ae530b1eb1ce798ec54bc9b45efdba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_compressed_texture_s3tc.dart", "hash": "1d64df0e3ebd5eb34fd94bbca3c3ff87"}, {"path": "/Users/<USER>/Desktop/66/web/icons/Icon-192.png", "hash": "ad7efca123ce585fd13a3d034d7348c4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/src/in_app_webview/headless_in_app_webview.dart", "hash": "a11ae22038182582bc64138902f6b750"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/encoding.dart", "hash": "0fae4441d0dbf3ea08446e7036a88ddf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_view_transitions.dart", "hash": "ae2402018a3f515ea615acc40c8769e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sql.dart", "hash": "9ab11d900c41a880b39e97693f383b5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/src/interop/app.dart", "hash": "652606a74f7630ee0060392b056e5062"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "7e0e723348daf7abfd74287e07b76dd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_webview_asset_loader.dart", "hash": "451c9597480528c668ad781195e38032"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "5638f5f2028c522b32626825f6bd5b7e"}, {"path": "build/web/icons/Icon-maskable-192.png", "hash": "ad7efca123ce585fd13a3d034d7348c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/orientation_event.dart", "hash": "00ce625f6c9a3d5b0cd196994fdbaa0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/find_session.dart", "hash": "ba687ba1fd4857a00a0e79baac1b9e38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/src/firebase_core_web.dart", "hash": "0cde83eff962942bfc19ae2d0d05c87e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/global_state.dart", "hash": "dc4e3bf96e9c6e94879d54eaa2f81c69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/package_info_platform_interface.dart", "hash": "022ddffcb01934fc1b0912fcb38de832"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.56/LICENSE", "hash": "e8b32b6d7c1328dfb1968caef8249452"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/javascript_message.dart", "hash": "f8107ebf50885cb4b6c1bb0bd35b0af7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/algorithm.dart", "hash": "c9387406fe26aecc079e1836e5be70f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1bmpstring.dart", "hash": "6947916ae1dbdc54f4474073e00d3679"}, {"path": "/Users/<USER>/Desktop/66/web/splash/img/dark-3x.png", "hash": "83bc7b765cca0d749dee3db9e14a0405"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "a55ac84003178cdc783ca41a634500a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/safe_browsing_response_action.g.dart", "hash": "4931e7b797fa299a983ab7725f5469c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_typed_om.dart", "hash": "a7dc7f54b0300393880ad5ea5e4db662"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1utf8string.dart", "hash": "51d920b8517f9552fbc53355b0aedde1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "2ad27cdee5e6fe69626594543bd0e7c4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "245a31a30063b63cbfd631fdc2ddf0d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/content_blocker_trigger_load_type.dart", "hash": "2f31771b8f445e8e6fa1f8a3ae7d7565"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/paddings/pkcs7.dart", "hash": "8c54825eb6ecf25c79adfb0d890858bb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "f9646c35238459f46dd9d87783813f08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/compact_number_format.dart", "hash": "4d3e899568e228c77a15b84754705d4e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp128r2.dart", "hash": "b169ef1943f4cc4ec4d8f5c56b25a622"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/shared_preferences_web.dart", "hash": "5261c2f8204719c9c489eed805f72cdd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/import_mixin.dart", "hash": "50ef33e165498030b82cc4c8d8408597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2/LICENSE", "hash": "2a68e6b288e18606a93b3adf27dbf048"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/custom_vimeo_player.dart", "hash": "68874ca36d4a23fa46da447511905c1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/lib/src/method_channel_wakelock_plus.dart", "hash": "b16e2077de3998c5282c696dee947bc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.14.0/lib/src/firebase.dart", "hash": "5ac6d992f5cbebe5e5d4e8bc4ed5ae6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_resource_response.dart", "hash": "bfb6aec45bfef7c30313e5c580b8bac6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier.dart", "hash": "e07baf43a89b4a1225ab8dab1161d2be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/firebase_core_web_interop.dart", "hash": "8fe7558f8911c5ec845b3b45d253faee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_message/web_message_listener.dart", "hash": "b5b94e648279b59ae175f00e224961c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_key_derivator.dart", "hash": "15503bd3441ce266624f6509e5a7d6e3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "0ff55be19444856c892e701c475b20f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_request_cache_policy.dart", "hash": "040cab7ba2af1f6df1aa16b7bf80257e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/parameters_with_iv.dart", "hash": "dcac631b5103a400096235ac0d1b8a85"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "aed826e965e4aa2fdb3466d39e33d824"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/lib/method_channel/method_channel_share.dart", "hash": "a27e281f7f3b1a2b17fa82a3e3bc0421"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/gostr3410_2001_cryptopro_xchb.dart", "hash": "4e85d0625d482a63b08cca9927502aa6"}, {"path": "/Users/<USER>/Desktop/66/lib/services/api_service.dart", "hash": "62e43953fce52cd450e849f254a304ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/pull_to_refresh_size.g.dart", "hash": "ed601a0916bb146d689342ed5a139454"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/fonts/fa-regular-400.ttf", "hash": "3ca5dc7621921b901d513cc1ce23788c"}, {"path": "/Users/<USER>/Desktop/66/hosted_vimeo_player.html", "hash": "4c49eecaa32beccfa2b0b7801b6779c4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "57b508bc908fd0950889e1d70ce36fdd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_storage/web_storage.dart", "hash": "30867b262fcf6a163b2cece7a74c6aa9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/LICENSE", "hash": "39d3054e9c33d4275e9fa1112488b50b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/src/web_storage/web_storage.dart", "hash": "cc31bfa3f66cdecf2163ee764182062c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "build/web/assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf", "hash": "43d81179d8a87ad8533c6563c72158f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/context_menu/context_menu_item.g.dart", "hash": "a04192631525b3aebdc429db11480f5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/ref.dart", "hash": "452cd5bd89dd73f555cc1ef42032e1f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_until.dart", "hash": "22ce2f0be207fd716e4ae18e312f5cf0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_webview/in_app_webview_settings.g.dart", "hash": "e2dc19834710e9c171d583bbd394ea84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/android/main.dart", "hash": "17b21b146815cfb066c70c2ee23c010a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_archive_format.dart", "hash": "c08713abb0416b91eb5aaf32dadf5e22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/lib/src/webview_controller.dart", "hash": "7d53e8f2c1a83279b3ba08bdfe9ad0f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/apple/main.dart", "hash": "6510d9a1871fee6505ed48dd9d0c55fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/internals.dart", "hash": "5c4a5af039aad32f5ac9bdbfc1536af4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/ige.dart", "hash": "770bfccaae45c6029de0af97430cc4a5"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/nutrition_page.dart", "hash": "d7e557e61fc2f384c92cee9488a1c44c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/drag_boundary.dart", "hash": "1e0ea989110b1544dbaf1fdf3d9864cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/web.dart", "hash": "6d61c054b2c590f89f518959b29a2002"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/validation.dart", "hash": "af69b927cad3da3ff26f5e278d151304"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/src/interop/app_interop.dart", "hash": "a6f3252179428494dd5f87f8d9903207"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/context_menu/context_menu_settings.g.dart", "hash": "53de3951a28818bc7c5b7eceb9768498"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1set.dart", "hash": "27a0bdbf6e90ba19c5f6c80fe677387e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/html_input_stream.dart", "hash": "ed02ce14880085c75d4dbc4b3145371d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/base.dart", "hash": "d0b83bff5ce65e6924939f442ae2c2a7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "44d59e37041b6305018f70012fef7d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/event_timing.dart", "hash": "303647c527ea561eec5969c76138b1e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/src/in_app_webview/in_app_webview_controller.dart", "hash": "4ffa542c47ef33d7e77ac5c84e304e29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/ignore_elements.dart", "hash": "908b86c4378330e5b303026c8c3e29aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vimeo_video_player-1.0.1/lib/src/vimeo_player.dart", "hash": "9a2efe161b0d183c28074d60fb0abac6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/content_blocker_trigger_resource_type.g.dart", "hash": "cb19efb444cc853b710bb5f994a3687d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/private_network_access.dart", "hash": "7cf0d50888c845f6bc217f8c2f6e3826"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/notification_model.dart", "hash": "8037846245c5991d686a55ccc2d28d5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/image_cache_manager.dart", "hash": "69c7e246c8fb227cdabc8a3d9a8316dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_pagination_mode.dart", "hash": "abe13cb6d43bd3f729fbbadf96c12264"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "a2587417bcfd04b614cac5d749f65180"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_message_callback.dart", "hash": "7539a54ce4c309cc69a11ec39f33107c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/cshake.dart", "hash": "26f7e7d2614abc62e37602284c38e8c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/in_app_webview_hit_test_result.dart", "hash": "46b70ac2c1b4420655d70fdc1e7fc6dc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "e138cb83b907c09a4ac468dff69d43de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/security_origin.dart", "hash": "a0911314e0a803a26ec80a7008d91fb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/helpers/cron_helper.dart", "hash": "da80fd65969624be25290cd3bf5cfcb6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE", "hash": "c17706815151969aa7de6328178cc8bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_io.dart", "hash": "7caf4c65583e594208feee7e60872fea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.0+1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "674ba42fbba2c018f6a1a5efd50ab83e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "7755bff1bceea0db42330320ad10baad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "ec0bf24485bc5f9b825a382457f586e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/content_world.dart", "hash": "c11ec7677831368c737051393e196b5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/in_app_webview/headless_in_app_webview.dart", "hash": "6fac84ea76cbe54528989bdaf7a0b38f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "a309d8ca64c3efb3ad74b742ffb0e1dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/javascript_mode.dart", "hash": "1ffba8301a3aae3f81db122d65fb1333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/webview_environment/main.dart", "hash": "4fc12411964967599e375ff2ec43052e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/call_async_javascript_result.g.dart", "hash": "92463f5293730e37363fe70d21858273"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "04c960ae6d770135bb0b6acf14b134a4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "e40877daa15509fcbd3e465d246dbc09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/exception_impl.dart", "hash": "7b3fbf91245e315040bd120bc9bf51ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/group_alert_behaviour.dart", "hash": "493572ee942bac727a4225c0e61852e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_authentication_challenge.dart", "hash": "cf09d28b2d8f5fe8258a4a4ff3c26d7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/meta_tag_attribute.g.dart", "hash": "e8e3c6685e9f56c474aa384dbb7aacb5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/logs/logger.dart", "hash": "f12bf29a07e149de74a3051849f0dd71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/url_launcher_string.dart", "hash": "ec94194f35d48443f468a3b06ef69845"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/chrome_safari_browser_settings.dart", "hash": "b155bc8dbf6c1a873df26444c47cece5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/layout_algorithm.g.dart", "hash": "48bb9a76176a8e993f162ab94f802bde"}, {"path": "build/web/icons/Icon-192.png", "hash": "ad7efca123ce585fd13a3d034d7348c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE", "hash": "612951585458204d3e3aa22ecf313e49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/find_session.g.dart", "hash": "0b39a2ce2ccc1fc8d1b6027658a6d19f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "44c1268c1ecafd3b4cd06ab573f6779a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/navigation_request.dart", "hash": "9b3f56e1159df163cf4f2b13d1048e79"}, {"path": "build/web/flutter.js", "hash": "76f08d47ff9f5715220992f993002504"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/oes_texture_float_linear.dart", "hash": "c7027f3f13166997500119a5cc6e3732"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sqflite_database_factory.dart", "hash": "b2b96fda3b5d147408ecb71c2bbe73a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib/flutter_inappwebview_internal_annotations.dart", "hash": "5e60ffa79c39ce80c0763f948f09dbe2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_m.dart", "hash": "167efb1e5d1b6fa8a22f6454fbf2a9c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/custom_tabs_navigation_event_type.g.dart", "hash": "e2b10a6de42f1b9499086b58224db819"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8.dart", "hash": "e3d03ffb9ffa123af98df771a98759c0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "9ea1746a0f17f049b99a29f2f74e62ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/printer.dart", "hash": "168df457c035f90bd1a76c6d17cf967f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier.dart", "hash": "b60a2076a519fde0c9162319239b25eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/url.dart", "hash": "03c1300d573d0b8d79399464a2d1bb8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/context_menu/context_menu_settings.dart", "hash": "5889e33a00cb04fbf00231e18bd64b55"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "43ba7557388f413902313df64e072389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webvtt.dart", "hash": "a50e79e8234b2f6a058726e5a910ffb3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "a88d8ea7c8c98dd1d35ad2853f04efe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_masking.dart", "hash": "2e81446170dfbba4057d307bf888d364"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/listenable_provider.dart", "hash": "fe16b487322631b50c3cbb09de987315"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "84f94e87e444ce4ebc562b2707348a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/media_playback_state.g.dart", "hash": "8f0f6d4f8f41a79ca1fda464c0b1ce38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/attributed_string.g.dart", "hash": "7844ab21a000b480c129e8ec7eeda16c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/awesome_notifications_empty.dart", "hash": "636d643115e504892e9be4ad924c8d6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/webview_environment/platform_webview_environment.dart", "hash": "1eea952b512ab192aa068f5629507e17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/octo_set.dart", "hash": "0d750078c87ce8f99c60c3c76305c11a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/mathml_core.dart", "hash": "e3f8daeff0664c49cd50ac275a604523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/web_bluetooth.dart", "hash": "e29eca80b023da19b121fc3e372ca847"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common/env.dart", "hash": "f23b1cec674b4863aec7961f4a2ae758"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webtransport.dart", "hash": "497331f651ef215d8b51429e95e0c9aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.0/lib/src/analytics_event_item.dart", "hash": "e55be4ac4be4bb682665b22e05db5caf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp192t1.dart", "hash": "8d258031864a092670b4e4d486d371f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/src/inappwebview_platform.dart", "hash": "81dee3741ffdbd9b5d16459c39f1dbdf"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "7f662c8207cea5db3d45f239a277ca9c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/security_origin.g.dart", "hash": "00b689c8317efac415f62802f76f1c68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "82ea4f7076bd7e32c383a2466518b943"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose_family.dart", "hash": "0dd5377006ddfc0b63c193276ef02d43"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/premium_splash_screen.dart", "hash": "951555621988d5bd1980134efa01bb5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/model.dart", "hash": "2078a2797211d6733852b0fe91c41c75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/lib/src/webview_cookie_manager.dart", "hash": "fbe8d6502b8b92d931738908e919108d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/dev_utils.dart", "hash": "667c5e3e357a840e3d3a6137458c0c34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/video_player_page.dart", "hash": "2684b1cfa8fb2548d4298e9562dee61b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/web_share.dart", "hash": "b741e14cacd655b8d9ce8fb1ed1034b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/underline_style.g.dart", "hash": "7a999121f2df3a098eaa3a3ca33c2f70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/number_format_parser.dart", "hash": "699fa08fa71f3fd7eef0d69703106acf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/resize_observer.dart", "hash": "a1f69f2ce4c211abb4f4ed797b152b01"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "737365e0b93f911e49f1ac1e5363564c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/utils.dart", "hash": "2ca48ade734e30ac0aa95393df8e12d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_webview/main.dart", "hash": "3176e43d6cda31f8a34967e4db4d9754"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "72804f9d34b9a247c43d6cc575527370"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/composite_subscription.dart", "hash": "76cc6c2b845bff11813d968688280b36"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "1e840a2c03797a7468018e124b957d2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_authentication_session/main.dart", "hash": "7af08495acaa831e8d471134830d7d96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_not_null.dart", "hash": "9b84f667016de96aa99b12338a4dfb57"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "ccb3c80f13485133893f760c837c8b62"}, {"path": "build/web/assets/AssetManifest.json", "hash": "74d2cb192f45f92547a04b09550ac654"}, {"path": "/Users/<USER>/Desktop/66/web/index.html", "hash": "11eae0f7302067b5eb0fc6521e7012d8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "505f6c9750f9390c9e9e4d881092cef4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/consumer.dart", "hash": "38c2b67895c0418bce6750d3751a5b26"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/session_logout_dialog.dart", "hash": "6edd8388815aef995bf044bebe2d6d6e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "e85b30de1963bb6981d72b6027a66dd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/cross_file.dart", "hash": "b5c8f4dba868efb80ed69fcd5a7d3f07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_object_provider.dart", "hash": "39e587e00bba5c8a7978fd25cf983cc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/css_class_set.dart", "hash": "fd47de61e362c730e345626317a8fc44"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "732535ba697d95c80d1215c0879477f1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "5cedacfe2fd447a541cd599bfc1aef91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/download_start_request.dart", "hash": "e5d3e072e4ffef78251e5873d4d1b881"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1exception.dart", "hash": "b1e77e9e7b9fcf421147af98dbf67760"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/screenshot_configuration.g.dart", "hash": "297bcc647d36bc6476afc40bb532aec5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/custom_tabs_share_state.g.dart", "hash": "60b00b8c748cf3bf05a93e24ac88307a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/trace.dart", "hash": "dcb1bf21d8afb364e20a47f106496780"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/with_latest_from.dart", "hash": "eab456316eb52f6f668d1dd2800c1085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib/synchronized.dart", "hash": "b62a2c91536fb75946b3193c9a5b04aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/notification_permission.dart", "hash": "50c3e61cb6a8c171f0fac6a19f0639a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/window_style_mask.g.dart", "hash": "4372dd1f8a673cc428a602c23a9a8243"}, {"path": "build/web/canvaskit/skwasm_st.wasm", "hash": "3e4cc56561a4cac78b18c30bc5e73055"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "15059e9824dd4a9e06136d8dfd91c26a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/LICENSE", "hash": "b3896c42c38a76b4ed9d478ca19593e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay_when.dart", "hash": "064ceadd31d0b29fc59188b2c4d45db1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "428549777327ddf7f2287b69cab7b68b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/page.dart", "hash": "6b16a4d19243ba00762af7e39dafc177"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/content_blocker_action_type.g.dart", "hash": "d5c3ee982c7837e5f4b96d61ef0c912a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "84e117adf104c68b0d8d94031212b328"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repository.dart", "hash": "d423d24bacc39262f492386b09a7ee7b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "13b920f66eba39405ab6c5487e5fc3f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/cookie_store.dart", "hash": "7309588fb9792c7b1e40d19ddb5f8fe0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/replay_stream.dart", "hash": "c86f575dce7f62595d9f1e169492f750"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "5da121a0d3087e7cf021bfcdeb247b77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/md2.dart", "hash": "5994aca748b376c7ba9e02b2344c61ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/tracing_category.g.dart", "hash": "529eab4178c0585e068964a05d3af623"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha384.dart", "hash": "9b963ea0af13edbb8f022ff4b9f58c55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/ios_options.dart", "hash": "704d7f872888ec6e9697123a180fd95d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/src/keys_extension.dart", "hash": "eccf57aff3bed39266c0358b9b81ae9f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "ddf1bde8f4b9706d5769690b7819e5d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/desede_engine.dart", "hash": "c81da903482180f19bfa397e4020bd52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/utils/lerp.dart", "hash": "10413a05296db73b1d2d00ab94054ba8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_attributes.g.dart", "hash": "fd5d5821b2dcbf61ad1ac6740ad5fa87"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "578ff911d6e70b239fd629f5a0206fd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ssl_certificate.dart", "hash": "513927539c7c2fef81f78319591660ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/dismiss_button_style.dart", "hash": "3439986d9aabb3f1d11a0a6db5c5efd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_a.dart", "hash": "6f31150716f793ef18c1216f785c7e6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_set.dart", "hash": "42fd90a5d728fea9edfaa04f4f1ee6d8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "b1bb8356cca8b86afca314ab4898a527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/chrome_safari_browser/chrome_safari_browser.dart", "hash": "89023769de81fe8d19ba9a258b0f89d8"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/kft_app_bar.dart", "hash": "46d2e3e45652f355905b4f1566e0dc78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/find_interaction/main.dart", "hash": "b10a78a81e4eaf6f3294755e89316f12"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "59b6b74779849bf5b836b84bb362b99b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_info.g.dart", "hash": "ac46d6d1b1df51fee122f6d3030b5f22"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "e0b4c38191be9320c3113762d2dfebbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sqflite_debug.dart", "hash": "a2cdec29e909752629150b24b9b18407"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp192r1.dart", "hash": "781f8d5b9335824b7f34534f48312377"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "b5bd9d15c10929b4a63ea0df649e2d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib/src/exchangeable_object_constructor.dart", "hash": "64bd8d26f04a6022a3fd2340e98b5a21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/base_aead_block_cipher.dart", "hash": "45b660175c00f44ef94fd58bd294258f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider.dart", "hash": "d5b1d01f918c452585a990bba4c2b919"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/src/interop/core_interop.dart", "hash": "bca1ec0ae4827fe641529fb3c7c2ffa3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/buffer.dart", "hash": "15563ca80dc06490676ca80b6b98718f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp384r1.dart", "hash": "28e04090f5c27de03d786a00eb52fb2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_generators/api.dart", "hash": "35da1e0ac4001efabe27f201740996b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/utils.dart", "hash": "7014dc257215cc17a58e5bf88855eff7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/should_allow_deprecated_tls_action.dart", "hash": "c345d616129c8ae7dd806e51acb68b39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/parameters_with_salt.dart", "hash": "8477e1afe76cb5334fd6cc42a04d0ff8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/layout_algorithm.dart", "hash": "b58732799fc91c58c674b5ea862e4ecd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/lib/src/wakelock_plus_web_plugin.dart", "hash": "a50ab092903b57be3c15a370e1f79da8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v5.dart", "hash": "cc8112e5daca3ae7caf3bd7beda5f39e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/bar_chart/bar_chart_data.dart", "hash": "2b1f46595a0891161ea82b8f92198e13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/gamepad.dart", "hash": "3b6116b8e01fe069a2233912fafbca0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/render_process_gone_detail.g.dart", "hash": "fcd860cf4f6beb0594349de1f818658f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/value_provider.dart", "hash": "d5a669dc5155cedc975db1022a570128"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v6.dart", "hash": "70ba25c403724d1332ff4a9e426d7e90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib/src/basic_lock.dart", "hash": "25057894002e0442750b744411e90b9c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1generalizedtime.dart", "hash": "1c5e7ae4634ee0e15c9e7a2c4e4bc7aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/webview_render_process_action.dart", "hash": "f786fd8db06c5fbdb8a17b8e919fbd72"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/auto_dispose.dart", "hash": "a3250d5fb60cc8b17997c886a67be737"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/navigation_response_action.dart", "hash": "4b13cd433e52702ab575679bbd58c0b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/family.dart", "hash": "c32553850c6990014c017cc3b3024df3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_manager.dart", "hash": "b188e0026dde1c7ef925b5efb80450ef"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "10505aa641207501d9a0759bf2d6515e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "d2386b256656121d501a16234b008e2b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "5de15d7a41897996ef485c087ef4245b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/time_and_date.dart", "hash": "ba3e62e96705516e59c93dde160d73b8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1dab3723527db6a19410ed34b6acaeed"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "8ece5be4aa5c8fa615288c4c8c5277a2"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/official_vimeo_player.dart", "hash": "986b74756f80f4a4e532a9f5dcde1155"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/LICENSE", "hash": "53d71cd0b0021b18407a0b37287e730f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/web_otp.dart", "hash": "29f075236669305716fe4d5d86d72403"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/compat.dart", "hash": "75e9e8da5881b6c2ebedc871d7bbc064"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/helpers/events/providers.dart", "hash": "1603827b24b2ef8333181f7b49d83285"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/analyzer.dart", "hash": "17aa54781ed25267f20b106de6b6d59a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_o.dart", "hash": "d14d602c73240e571385abe6192469f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_storage/main.dart", "hash": "1d3c92d821826d94d998ee23a88c3ab9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "a8fdf31698b305c9fdad63aa7a990766"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "cd995d0f309bf74d0bbe94eb1e4e8e81"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "a79a6f9bb06c7d6dc5fb74ac53dce31b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/line_chart/line_chart_helper.dart", "hash": "19a4955c13169b563c4496df8a9d6479"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/web/web_platform.dart", "hash": "569a5bcca8086c422a54faa21fc1cf35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp112r1.dart", "hash": "e2ef26fdbb565dd9481f925b51fc1bcf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/progress_indicators.dart", "hash": "c2c2286fb7180be54cc4fd8b03ba9dea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/platform_webview_cookie_manager_creation_params.dart", "hash": "0258e841a5e594b8720ba59a392bdff1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_texture_filter_anisotropic.dart", "hash": "0ed231bf9417c36ac7feb2ebd972b015"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/platform_webview_widget_creation_params.dart", "hash": "002d9cfb5f6d6dfb64b0acbdb4763b88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_utils.dart", "hash": "bf850e483673d93e76e1fd5c69d8135a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/fetch_request_action.g.dart", "hash": "f7d357f44a654a911cc34343f9bc8d37"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "04c713cbc0ac5e15c7978a2e91b81488"}, {"path": "/Users/<USER>/Desktop/66/lib/services/progress_settings_service.dart", "hash": "a673ca71cfd8c34be0ba9365692203f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/in_app_webview_initial_data.g.dart", "hash": "520a136c90f50c6567e06812828481a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database_file_system.dart", "hash": "dac02dc6cb13c753a5f3ae19976b1540"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_cert_bag.dart", "hash": "50ea93c36c3536adb8c3831a5ff255fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/x_file.dart", "hash": "d06c42e6c83be207b86412e11889266a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1integer.dart", "hash": "b4268a914c3911e85b21da6ed28e8b0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/link.dart", "hash": "c36f00a660d9aa87ebeab8672ccc6b32"}, {"path": "build/web/canvaskit/canvaskit.wasm", "hash": "efeeba7dcc952dae57870d4df3111fad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/time_interval.dart", "hash": "17db414327e1f763806c112c6f664ca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_confirm_response_action.dart", "hash": "689fea3ed922f89f7820f71c07b8f6b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/flutter_inappwebview.dart", "hash": "5e08507ee140179d1b20ac2a86bf9079"}, {"path": "/Users/<USER>/Desktop/66/web/splash/img/dark-1x.png", "hash": "4528f746fdcaa895355939d70cf75306"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "caad40ad1936874ea93473b300bb909c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/proxy_rule.g.dart", "hash": "dbdcd020fe625832cce04bfa8b069b78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/web/headless_in_app_web_view_web_element.dart", "hash": "7b249cf04863a37c3900af75f2789c3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/extensions/border_extension.dart", "hash": "13902009ed7ba0e3222984606822edb4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "490fffadb78eb29c5fe209be7fe12370"}, {"path": "build/web/canvaskit/skwasm_st.js", "hash": "d1326ceef381ad382ab492ba5d96f04d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8generic.dart", "hash": "00a661dfeb90c5dba43ec7e638141966"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "008b3ea4691331636bbea9e057357ceb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/macs/poly1305.dart", "hash": "adad7c45c2cfb5082b4bd61830653235"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repositories.dart", "hash": "8cf88d8eac6c9b46a6fb3877f9fc35d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/reassemble_handler.dart", "hash": "17dd5087a9b407563f662fc112624260"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/notification_schedule.dart", "hash": "e18b4b70a801bf12c1da5606bfd5bc0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/meta_tag_attribute.dart", "hash": "3c355fc4ac9091bf445390331dda8d57"}, {"path": "build/web/assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf", "hash": "00a0d5a58ed34a52b40eb372392a8b98"}, {"path": "build/web/assets/packages/flutter_inappwebview_web/assets/web/web_support.js", "hash": "3cb6d1dcd278493cb8b8a145aadb432d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/renderer_priority_policy.g.dart", "hash": "6d99cb5fecd09266b099fbb08c614ade"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/action_mode_menu_item.dart", "hash": "59233d5a556fe1a747b32c48984055eb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "08c3fd9ed1607d3a707ffe9b3532218a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/screenshot_configuration.dart", "hash": "e910b46830116a031e837a888a4b4949"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webxr_hand_input.dart", "hash": "97f94ad53103b6813eb26a6d64910efa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1ipaddress.dart", "hash": "30d691e618930682703db00c4058bb8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_authentication_session/web_authenticate_session_settings.dart", "hash": "34f8455e35fa2221f8241d535bbb293e"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/enhanced_vimeo_player.dart", "hash": "8476771038151d3b1fdf87a5aa106b90"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "04451542afc67a74282bd56d7ee454f5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "6aad5f436704faf509d60ddb032f41b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/navigation_type.dart", "hash": "8ce80dd77e91d730a01cf868916e0474"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp256r1.dart", "hash": "87c7f992b5cfb59a98477aeca4399c50"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "36fc598c656490ab430ca1be5fb909e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1enumerated.dart", "hash": "15df5f6a6c0927c61ff5584926c53020"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "cd7f8dc942f5138db121aabbaba920ac"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "85cf42bafb7c0646bd7a99379649da29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/exhaust_map.dart", "hash": "4c61dffec4ef48c5b09f3009e7765657"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/date_computation.dart", "hash": "37837bd1379e66f38e4a7775b6084d0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/assets/no_sleep.js", "hash": "7748a45cd593f33280669b29c2c8919a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_process_global_config.g.dart", "hash": "b069d3329d7508303f5d69983382814b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sm3.dart", "hash": "9191b515c6ecf451da97518f6307d935"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stack_trace.dart", "hash": "bd15738d49bec303fe3d234de40503d8"}, {"path": "build/web/assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css", "hash": "5a8d0222407e388155d7d1395a75d5b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/context_menu/context_menu.g.dart", "hash": "2fa92c950be91568f26d3f132f52eda1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "ffa4f7b2d5b1caccc05cf4b64021ae5e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "ee62fb3be5d885d65054fac4b84cac6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/javascript_dialog_request.dart", "hash": "a8d360ec037224d19465bed3ff3c7b7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/default.dart", "hash": "7f30d05e05b047b274b1c4b45391d698"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_color_buffer_float.dart", "hash": "784fc2946fba67fc31c328cbfbbf71a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/lazy_chain.dart", "hash": "7ec268e37049e5c22e226c94df1776b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/find_interaction/find_interaction_controller.dart", "hash": "b880d4f8e29d04754126eb392dd64ac1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/scheduling_apis.dart", "hash": "b2b6fe6c3aa455fbcc2731bade5eb5e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_confirm_response.g.dart", "hash": "4186328119f471796f73ea21849b236a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/app.dart", "hash": "66bb0d42812dbdcb77a351f5d79c74a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_before_unload_response.dart", "hash": "161a7423eaa36e5abbfb51646b9d09c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/loaded_resource.dart", "hash": "3c27b204354c7c5022777010820ef916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_octet_string.dart", "hash": "4712bf9834e41f575c82673a4c91c279"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "29befe23f841cf5dd2dc7df24c13d88d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "f996ce49eab57718350b84e11ea3192d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ui_image.dart", "hash": "22b74b44ac62c8e0218daa4363664102"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "39e18667c84e363d875147cc5dc6b2fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/x509_certificate/oid.dart", "hash": "19dc4f9c7edd727af63d66befe7545b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/console_message_level.g.dart", "hash": "96d6a02fdf0724fe7ccab5391b34a202"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/khr_parallel_shader_compile.dart", "hash": "4b5e75750af9287906939a58af8510de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_before_unload_response_action.dart", "hash": "98a9b89329565c7abec20f9ff44b33d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/logger/sqflite_logger.dart", "hash": "ab42e582c15854ab187bc7a07fb8baa5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/proxy_provider.dart", "hash": "57b51f6f00c6bc3a29abbf83fbd804f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/fork_join.dart", "hash": "848f74750b2ecf581969c7a0dd4c2c36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/fileapi.dart", "hash": "c41c291723be3c63d244abf8b69156c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_compressed_texture_etc1.dart", "hash": "7b2c75d16ca438685c32ac70d9af609f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_info.dart", "hash": "2e6d14256446fb664ee911cbb95711e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_base.dart", "hash": "ba78ae31f8b033543921d261bbe60dca"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "b61a261e42de1512c8a95fd52ef6540d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/navigation_type.g.dart", "hash": "ac9925152e818ac27902dac74f022c70"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/_html_element_view_web.dart", "hash": "e316b4b5ba047ce15b81f63c8a2dbba7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/progress_page_new.dart", "hash": "ab6171c81fb26d239875ddaf4483483f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "bce1e8ef07d9830bbf99031d77e0b9fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/src/utils.dart", "hash": "ce30848ef1f94b243d6094ee0d740597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_generalized_time.dart", "hash": "78a64ae6ed0887ba9aac8e9bf202d0e9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "6cae6900e82c94905cc2aaefd806f8eb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "3623c605586d2e37af23d6b746721bd7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "e324dd19cc02a1bf47bf7cc545dcca79"}, {"path": "/Users/<USER>/flutter/packages/flutter_web_plugins/lib/src/plugin_event_channel.dart", "hash": "895e81c8920f3a4770d534d845c4618e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "c0cf85f80b79542d2b0e1a00547d7310"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_blend_minmax.dart", "hash": "91dce3137bda013efb41522091668ef9"}, {"path": "build/web/assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "33b7d9392238c04c131b6ce224e13711"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/url_change.dart", "hash": "111d09c1c4c7e2aa16e664f0bd4e5322"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/fedcm.dart", "hash": "eb860bd33912658cc3569f94ce6cd7f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/x509_certificate/key_usage.dart", "hash": "c8fcff4ebbecfaa4173c12a0c18748d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_animations.dart", "hash": "82e2cce258d43f85fa85f1f226e8a30e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/loaded_resource.g.dart", "hash": "0e88de5cfeea365f4f75257ee09c74ac"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "68eb8647107febe1419211e153b27a54"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "0821fcdff89c96a505e2d37cf1b52686"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "9e64d24aeed0ce5534422c6e4b454676"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/lib/src/share_plus_linux.dart", "hash": "215ea407318b75782d0a36692e036ca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_history.dart", "hash": "d8fc831d93d862f26b6494ccb3b05aa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_webview/platform_inappwebview_controller.dart", "hash": "bb4c18fe7833c5a492b13e9a868a1af9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/lib/model/base_device_info.dart", "hash": "4f0e33807b3ea8a3a5d03a85dbdf565f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/prime192v2.dart", "hash": "7aeb1366a4c73e08ca0012106f87cb8b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "92137effa05660558f35cfc5845783bc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "b3f8f8ba0560319908ddb5d9480a5788"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "d953dedc9eee14dfb343f4c5988840c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/random/block_ctr_random.dart", "hash": "4b0e65a90e7a1fa31571f3eb07023516"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/firebase_core_exceptions.dart", "hash": "bc949707cfd60ff573b48a27b02f6756"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_config.dart", "hash": "e0f2b097829216421823bda9ec381cab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs10/asn1_certification_request.dart", "hash": "1668123cb1d9d7633ffb9ed04693e2f6"}, {"path": "/Users/<USER>/Desktop/66/lib/main.dart", "hash": "e774292aa3fbd00729c6816d29a6b39b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/connectivity_plus.dart", "hash": "9b43d6f9384a837bbd0d8474e2365c7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework.dart", "hash": "d856ca958740bf8a240738ad9e9e69c2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "a8513860b3b4c160b57ca6264bc0acf8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/signature.dart", "hash": "1e8c55180c1ddf0d1a142eafbd3536f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/error_and_stacktrace.dart", "hash": "491a33282b614f40bd0fbd3f3b3d45f1"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/daily_streak_ring.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/types.dart", "hash": "30d5630a3fc3405d3c7b476c75dcd8d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_mac_data.dart", "hash": "01c56cbc4a5e25f4c5b3353c402f4304"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/permission_request.dart", "hash": "eb7f645ec36782b136c67d3ed7f2aaec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/vm_trace.dart", "hash": "9a7022bcfa03c67d126e948062508201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/argon2_register64_impl.dart", "hash": "2ed75b1464a26700c114a0b17cdd6d05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/cached_image_widget.dart", "hash": "4310ddfcafc039210f0221a343c43164"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/src/interop/utils/es6_interop.dart", "hash": "8d2faebd7784a6923721d530aefb52ca"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/kft_text_field.dart", "hash": "ce48731a206ae44a32f66d75a2768a05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/constant.dart", "hash": "8d5660686b2687f3947b822758c82942"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_alert_request.dart", "hash": "9259095c4867774248e91efcfa9320a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sql_builder.dart", "hash": "389352f8e1ecdf1332ad5bcb395bf9c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/print_job/print_job_settings.dart", "hash": "4c69c4df1c3499bd5cfb61408a546f14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/date_format.dart", "hash": "f04fc570517ea65a792945c6521d5bad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/LICENSE", "hash": "aca2926dd73b3e20037d949c2c374da2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_browser/apple/main.dart", "hash": "c448e177ea9bee0de9975083eee6ab66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "bc3c12f9555c86aa11866996e60c0ec9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable.dart", "hash": "52138432903419f8457bcad45e5e6e99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_android.dart", "hash": "23149dd1dabb201f41ccacb25e322741"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/ec_standard_curve_constructor.dart", "hash": "f4c21a708c21cecc2823a65315ab890b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_storage_origin.g.dart", "hash": "593ee594b8a0b86d22b809d966126c06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/salsa20.dart", "hash": "c08888c73393c2a5803d8d53ad303835"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "726a60283ea6c3a38fbb1ea6139cb4f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/radar_chart/radar_extension.dart", "hash": "768067e738f8af0c773a71c3e454910f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/javascript_console_message.dart", "hash": "8267479fdff528b1591cb141fcc999d5"}, {"path": "/Users/<USER>/Desktop/66/web/splash/img/light-1x.png", "hash": "4528f746fdcaa895355939d70cf75306"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "bb7bcb463df2ae0f5f952d439fdb384e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/base.dart", "hash": "63b92eb56c14d5474db11677f1800c83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/chacha20.dart", "hash": "334388262777ce982aae13d484f214d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/file_io.dart", "hash": "f520d3a0f6046d2786e577e0cb7cc2da"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/material.dart", "hash": "f485bc1aa4fbdf87e17bfb8f80e39258"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/web_locks.dart", "hash": "d9468725a679cc7859966763773626d0"}, {"path": "build/web/canvaskit/canvaskit.js.symbols", "hash": "68eb703b9a609baef8ee0e413b442f33"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "2e074f4fb954a719546377c67cb54608"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/default_avatar_widget.dart", "hash": "072e18262427b4094e1fe488b91a328c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "68c724edcc385ae2764308632abb76b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_object_identifier.dart", "hash": "06bd3bc262afab597464a920ebe98032"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp256r1.dart", "hash": "9af42975a39b3169fa2a3ff5bdf5aea3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "9796b800122953ccb2c3f40ba2120a94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/dom_parsing.dart", "hash": "723a3d6fbd3de1ca1e39b70c5ddb5bcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ajax_request_action.dart", "hash": "81abce8267b9eaadf69bc53977b2bb6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "ee2f417f35b5caa4a784b24c1bc32026"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "31b0d2bf647a0ce615f4937dd5307b1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_credential_persistence.g.dart", "hash": "cb7157f5a7aa42149cfa229bdb8da5ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/websockets.dart", "hash": "584d768370a6ea5d7aa43bc6dc941786"}, {"path": "build/web/icons/Icon-maskable-512.png", "hash": "ce3d4a1216bdefdc14a07019b7fba41e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/_timeline_web.dart", "hash": "bcb523bf43b06a185dcbbb6ab939edbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_sink.dart", "hash": "89e6e01bd627dc1cc46b16ae027742dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ajax_request_ready_state.dart", "hash": "5b728e4a88ceb3d569ead499cc855113"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp192r1.dart", "hash": "ff3b9eeee0120faa24ef23172fc5bacb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/sheet.dart", "hash": "0e0b94d805e193b69802ca99d5a51b27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/scrollbar_style.dart", "hash": "e10ae71e0d145003e5998d94111a8bf4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "53b9028402187f878713225b48bdd5bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/method_channel_connectivity.dart", "hash": "3d18e1306d78e114f98c9dc311fbf158"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_storage/platform_web_storage.dart", "hash": "87cfa352d06400883bb97588a090a405"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/xof.dart", "hash": "9868fd3987387a459685bafb3372ec41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asymmetric/oaep.dart", "hash": "b20477f458f2d1ac22308240b4fabda8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/sic.dart", "hash": "c03d43a0d1a5a8207fe25c92c362a7b4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "b1884cfd8778cd71cea03ca8f4b39f4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/builders.dart", "hash": "ccd0c138d8f151e1ccec18f4ceb98f01"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "3303320b233b1ca33a9e6e8c93e2d2c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/lib/src/model/android_device_info.dart", "hash": "45c88f72c13ad56caa756381980ff871"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "cd7a7fd807697152dfdaeb3109e4f4f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp224r1.dart", "hash": "6e5c4bc7b03bcdfe3f17bc6d9c3efcd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/asymmetric_block_cipher.dart", "hash": "93ccd61c35577ea95e16ebca0c058494"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "4b50828d394e7fe1a1198468175270d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp384t1.dart", "hash": "c1912c537c302fffda915c4c03584ce0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/scan.dart", "hash": "352139677d0d5e7dbf8941093403250b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "9c13d1f810b039faf38c54f062c83747"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_shader_texture_lod.dart", "hash": "74d1e8a2fbc012cc4c5589defc75f038"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/x509_certificate/asn1_distinguished_names.dart", "hash": "6a5cbfdc5d6665c7b16e1915e6619e12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/web_animations.dart", "hash": "ce0df8c9dd9f2b269d63313b9ed06d24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.56/lib/src/exception.dart", "hash": "9a74595c2e95795b6c96d74f2b6bcca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_device.dart", "hash": "5de9b4234c869bfb7f58138e26207e64"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "160e007517eb9af8299b242a217c6ff9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/console_message.g.dart", "hash": "8de4d11eaac7de49dafed5250888aae5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ssl_certificate_dname.dart", "hash": "098b5424ced6637ad17c7ce75da0715f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/platform_webview_widget.dart", "hash": "2f8bf8debe62625220a5d437ba42ff64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_safe_bag.dart", "hash": "da5ae7edadbd48f3bf8538f8649249b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_impl.dart", "hash": "8e1d2c37f506b65c7d8b3274456d8dfb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "24094ce9de1b9222a8d6548d3c01045a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/treebuilder.dart", "hash": "2c8ef2ed22dd79552a4d286b31817a27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/types.dart", "hash": "3353f65796638e830b18ffdf1a678a3a"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/workout_stats_card.dart", "hash": "a3f336f8cf8f123b07ab9e994a0918f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/md5.dart", "hash": "5828730d8f759fcbfa757299a6b2c4f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0/lib/src/exchangeable_object_method.dart", "hash": "de5b2e38ce0884ac5e7e7b8d069232c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.0/LICENSE", "hash": "95fbbb6a31fd950ed3b6f95caee42345"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ssl_error_type.dart", "hash": "80fc1be4f42e1fe065b5e554a3223242"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/selection_granularity.dart", "hash": "25c4f8c4192373b48668e6fba0fdac32"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "bb020f793a10d8bb46c0bbc996bd0bfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/notification_importance.dart", "hash": "a30f7a5fd33bc621a25ae6eb7ced9e23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/lib/src/webview_widget.dart", "hash": "846a92e32033aebf05944ee7d4e7beea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/helpers/cross_origin.dart", "hash": "c63cb9a1cdec2c4ed2b466377b08b694"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "5d8e29422039d9dcce6908b427814d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/notification_calendar.dart", "hash": "091eec5b0ae6a2040ba568f55e4575e6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "266a40131c9f05494e82934fd7096ed0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/utils/media_abstract_utils_web.dart", "hash": "9ca96c58f093ec0857d574f6d2a1e6bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/shake.dart", "hash": "8b81bdca02ee9cea674d06d456940b5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/battery_status.dart", "hash": "d8ec7796f593e2c27622cf1982f24c33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "853b1406f2756bef671f6d57135606f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/main.dart", "hash": "07226916fe0497f2f35ce9bfe0449e8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/web/in_app_web_view_web_element.dart", "hash": "ec6b395bb6c5ea3d88fd6b28e1bc5c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/force_dark.g.dart", "hash": "6c2c8443e3ec346cf318a7b9d7b1906a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "c761b80666ae3a0a349cef1131f4413d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/over_scroll_mode.dart", "hash": "a15bcceaecf26e357e99a057299c337a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "c9111e47389ee4b70aab720435a2a2df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/date_symbols.dart", "hash": "83e1307f3d3d50e9d6692543e689f91a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "9c053b0efcabd70996cc27e9d6c9303e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "c7c757e0bcbf3ae68b5c4a97007ec0b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/impl/secure_random_base.dart", "hash": "a5293efb82d89d837837df349d03ff7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1utctime.dart", "hash": "06ddbe4bde64b07e4f14292fd52fc253"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_storage_type.dart", "hash": "f29bb6346ff4cc2ac71f2ca0b1c2a93b"}, {"path": "build/web/index.html", "hash": "9c696a55a765e12b6e2c8938517c5ab3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/src/firebase_app_web.dart", "hash": "bb24a10aa3d65c8e52e4136f24dc6bf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/assets/t_rex_runner/t-rex.html", "hash": "16911fcc170c8af1c5457940bd0bf055"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/lib/js.dart", "hash": "09c3c90eb1e4d3e76794843967eb62ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/x509_certificate/x509_public_key.dart", "hash": "284337f294634e84ecf04a748f502262"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/src/in_app_webview/main.dart", "hash": "3591b5089853a9ec0a6dc3d742920ec7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "6f516ffde1d36f8f5e8806e7811b15ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/dematerialize.dart", "hash": "6f6ced37453e06f063a482bcb9509370"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/in_app_webview/apple/main.dart", "hash": "9a5d23b171c0f756541fc63e789a2ac5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/LICENSE", "hash": "75ba7e8a7322214ca6e449d0be23e2ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/line_scanner.dart", "hash": "168bedc5b96bb6fea46c5b5aa43addd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/block_cipher.dart", "hash": "ece6cc53a178d8bb370eeb451754f366"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/in_app_webview/android/main.dart", "hash": "9a5d23b171c0f756541fc63e789a2ac5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/website_data_record.g.dart", "hash": "99ceeab32cd119cc6a4a8531e9169116"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/internals.dart", "hash": "6683b2c06b0ec964286b1a54f7e2803f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "d2372e0fb5a584dcd1304d52e64d3f17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/mediastream_recording.dart", "hash": "45a6578b2c1f76cf920d26071875cc45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "f26f519ea124441ec71b37df7cfa1ee9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "65c7fba34475056b1ca7d0ab2c855971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/received_models/received_action.dart", "hash": "340295b404b45efacf6f96bce22fa097"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/platform_webview_permission_request.dart", "hash": "427bad4fc6f32394ca119b36556838b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/lib/src/pkg_web_tweaks.dart", "hash": "54d0eb0b57c4041fb6863d1b11bdf4df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/key_derivators/pkcs12_parameter_generator.dart", "hash": "509c42eb7f8b047ecb2df2ad0c0973b9"}, {"path": "build/web/flutter_bootstrap.js", "hash": "5dd8ca6fc07e76e7500635a6056496ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_history.g.dart", "hash": "84fc255d90d9d56274bbf2a9428d058b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/fetch_request_credential.g.dart", "hash": "adf5d71ded629c4e89747b07ad2d46b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/algorithms/fernet.dart", "hash": "844e459e0d8ee6db01c284160ba99ccf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_message/web_message_channel.dart", "hash": "aae596058ab33ebc453e41a793beb1e5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "e05529d31a09e4c86cde70483824fa10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/scheduler.dart", "hash": "1ac1f41185397129f7ea925130f188f2"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/progress_card_new.dart", "hash": "2fb5361a6c2b8f3686796b3140214bb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/parser.dart", "hash": "668feba83ac51da82a0cd90d035b271b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/pull_to_refresh/main.dart", "hash": "94fd781569162cf18a7602a46ec3bc76"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "89aeee125822690cbd46b2ff43c76ec1"}, {"path": "/Users/<USER>/Desktop/66/lib/services/persistent_auth_service.dart", "hash": "402f2c400c1f3322950cd60dd36c0c01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "9d2e926705e7e23b2e34aa022cf55324"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/permissions.dart", "hash": "210c048047ef1101085956c33ae275df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/navigation_response_action.g.dart", "hash": "1b51910ce814ef7d683ace0d5fe20110"}, {"path": "/Users/<USER>/flutter/bin/internal/engine.version", "hash": "32c4a9b6e6aa250ffd4aba05be285558"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "a6d730f196620dffe89ac987b96ef6c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/do.dart", "hash": "96594345ee8d89e2fd23dbca09121153"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_disjoint_timer_query.dart", "hash": "ec7ad138dbbbbb8da89674e3f9d8250b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/trie.dart", "hash": "f67497a47a5f8508d53dea861aa1e7ef"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/premium_card.dart", "hash": "ec2fad9c37171d19b47649c1b73bfd9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/preprocessor_options.dart", "hash": "9f788a6e170d7968e9906e4d470e07f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/dev_utils.dart", "hash": "9a4ee08ca541303a2aee95f83f548ce1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ebcf3ce26dea573af17627d822e9759"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/video_player_web.dart", "hash": "2e4839bd363cf57a2542f14c6b14e9ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sqlite_api.dart", "hash": "5494fe877262550facf407b379edae59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/utils.dart", "hash": "d84ae47a3c688bd889f442426f39be3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "0f2a1a61119c0bef3eaf52c47a2ebcf4"}, {"path": "build/web/canvaskit/skwasm.js", "hash": "960d3a3b9eb3a670269c98efe3c443ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/lib/src/device_info_plus_web.dart", "hash": "0ec3fe323126396baa61f377f500b8f6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "a2aa815908f2e15493e374b9380e558a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webcodecs_avc_codec_registration.dart", "hash": "5ddb1b86eeab0b5ae860487e9a07907d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/date_format_internal.dart", "hash": "125a884a4733a2ef5a572ae55d49e678"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "9bf11cc1ea784a251bf67350f02f910f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/form_resubmission_action.dart", "hash": "5c7c059ac93a1aed374975a0914cbc14"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "4aeb4635d84df42e6f220aba366af7d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/logger.dart", "hash": "0cdc9d79f7fc4d0920bc6a8fc02e6872"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/platform_ssl_auth_error.dart", "hash": "32c0fe60f39c35b80cf6cac25832289b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_slowsinks.dart", "hash": "76b9af381da547215b8af856567ae186"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/flutter_secure_storage.dart", "hash": "5a944801c9b2bd3447f982168b31e46c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs12/asn1_pfx.dart", "hash": "4438a8f51cd1bb37203275181971feca"}, {"path": "/Users/<USER>/flutter/bin/cache/flutter_web_sdk/kernel/dart2js_platform.dill", "hash": "4bc9511ec74c125b4eabcc4a66819845"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/payment_request.dart", "hash": "9f20dec3fd81898daaa4ab5f9547874d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_webview/platform_inappwebview_widget.dart", "hash": "efff746a85e91e4c236907097125e80c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/equatable.dart", "hash": "1a5f064d497f9539e8e2cb4ba15a8f05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry.dart", "hash": "66c3d8022ecd26ac3b2f30fe28e4c475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/mixed_content_mode.dart", "hash": "1097c3351f61440201aa9370d278f231"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/cupertino.dart", "hash": "9b83fabf1193bf4967b740dd7a2c8852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/chrome_safari_browser_secondary_toolbar.g.dart", "hash": "3edf23f939d38378d090bf65130eda98"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "262d1d2b1931deb30855b704092d3cb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/auto_dispose.dart", "hash": "9eb3cf0f33c573aa9e8424441db78539"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "c8260e372a7e6f788963210c83c55256"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webrtc_encoded_transform.dart", "hash": "c070aa3ca91b493eadd482d443fbd762"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/url_launcher_linux.dart", "hash": "9d67bda83980287cc1100fe7fad9e05d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/cfb.dart", "hash": "18469d4f463892d53ca2c320d7dd6450"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "d390b15ecef4289db88a4545e359bc8a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/provider_base.dart", "hash": "ddbfb4de9e9dc40a09a6bfae74a41dd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/xof_utils.dart", "hash": "2fbe3c5de5fd960b3af2656edc846d07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/LICENSE", "hash": "53d71cd0b0021b18407a0b37287e730f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/pull_to_refresh/pull_to_refresh_settings.dart", "hash": "3d50f3acdfc0ded4795e67080ac026fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/web/platform_util.dart", "hash": "4472cb464e920ea25a6754e9ed8dcd74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/container.dart", "hash": "8597f18181783d905e40dc64f0c0555a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/lib/messages.g.dart", "hash": "634f2177480aa7342f37029ca020fc45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/svg_animations.dart", "hash": "b23ba9698be55510ef57051143f4d8b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_attributes.dart", "hash": "391f90792c40e2e30be4c70aebe0a5b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/src/interop/core.dart", "hash": "7158e83a4ebeec62784ae3aba3d5f72f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "f209fe925dbbe18566facbfe882fdcb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/renderer_priority.g.dart", "hash": "7bcacfe3c2a43a322ba13ea0ecd595bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/asset_manifest.dart", "hash": "604151cdbd54ee0f0f4681fc8840d827"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/in_app_browser/in_app_browser.dart", "hash": "38e6f82f09ff24bef5de8ee15ef3bb6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/cipher_parameters.dart", "hash": "7584e95f9a7adfa3131ea1bbe7d81088"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_type.dart", "hash": "93f43c6a287e8cd98477a02e6aa0da8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "22cb97b7d09f329bab7ed148b4d181e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/key_generator.dart", "hash": "644bc34867e359c425f1a2d8fd16348d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/int_formatter.dart", "hash": "e6646f76f04f9456f5984aea312a50e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_controller.dart", "hash": "30b3454341d40c187ec21020db3a495b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_before_unload_response.g.dart", "hash": "b373bf6d4efe3b7148a8106fe7017410"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/painting.dart", "hash": "4bd60bd8ede4b9dad954493d26d3e586"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/utils/string_utils.dart", "hash": "f81b7f8ec3605f306f843862379ce6b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/string_formatter.dart", "hash": "b5871241f47bc90693cb26fae0bb8616"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "2d4b5a2778f275040b5e438045607332"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/x501/asn1_attribute_type_and_value.dart", "hash": "bfcbc06b75397407cc5bc0c3c9775c84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/lib/src/duration_utils.dart", "hash": "1a5532d9bb7f2f09d496e1b3bfdf18bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/family.dart", "hash": "18d9d372c2f7421114cc2a2df21d8206"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "78ce7527fa364df47ba0e611f4531c2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_g.dart", "hash": "edafd82e0b999bc51b79c8a3561ff1eb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "ebef4cfdfb854b138f6bdbbf53e73f0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/html.dart", "hash": "ca830189d7aafefe756316844e568c2e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "b3686e0781f3148d75a64ebb2bfef609"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "5da306e7f2542e5fb61efff6b4824912"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/cookie_manager.dart", "hash": "a48d95cb368ea4c0ccf9b01aab2f30cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/in_app_webview/apple/in_app_webview_controller.dart", "hash": "9e2fe3478f5e8af3e2e7f37cd091f7e3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a2d1c7bec7b52901761f3d52a1ac02d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/http_auth_response_action.g.dart", "hash": "ce72c0f7b2a17e447531160e4fff7423"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/LICENSE", "hash": "2abd2c9a42d4caf2b4f1640d68b02fd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_before_unload_request.dart", "hash": "97f67bc1e3beb6f7d6ef0e1cfa7893cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "hash": "5d89b1f468a243c2269dfaceb3d69801"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/data.dart", "hash": "e0b6567371b3d5f4cc62f768424e28c9"}, {"path": "build/web/assets/AssetManifest.bin.json", "hash": "9041263fa8017f7b036bc91aeec20635"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "f20071b459b9bbb98083efedeaf02777"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_history_item.dart", "hash": "d45e23f744be3b64455f0e2724899f76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/public_key.dart", "hash": "adfd4b4c9f127d6d7b0278d25c16cf75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/http_response_error.dart", "hash": "d76f0b6a50f3fe2b8140468e37ae43b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/key_derivator.dart", "hash": "23349969cad5e562b8bc82bb068b8b9c"}, {"path": "/Users/<USER>/flutter/bin/cache/dart-sdk/lib/libraries.json", "hash": "699f9f78cf04a3745fc01633f735be9d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "b33b1182e92dc3469db2563a33be2841"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "055a5c4a10cb9bc9f1e77c2c00e4ef9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/navigation_action_policy.dart", "hash": "4da3bdad73a32ebb64cb8b97e7404084"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/axis_chart/axis_chart_widgets.dart", "hash": "702256cb17c4257141132ae8b3aa22b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/platform_check/web.dart", "hash": "abfd018f1e39011ac785c25800c24dbb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asymmetric/pkcs1.dart", "hash": "34cf46d3d1d6fe648207862c6e4aae80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/ecdh.dart", "hash": "da5bab9eb0ad94cdf2fec0ada2a8c2ff"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "9434ff8aa06e13d5981ed6ec15eceb64"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "e5b4b18b359c9703926f723a1b8dd4ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/navigation_action.dart", "hash": "3e9f03dd7d45f29102607817c16e757d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/javascript_log_level.dart", "hash": "d18499e5e3e572bcc05667dfece7dd8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webaudio.dart", "hash": "c9f9523e7096a2ab94085888a12cd9be"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ae1f6fe977a287d316ee841eadf00c2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_archive_format.g.dart", "hash": "84cda866287f7855c684d39fdc4a695c"}, {"path": "/Users/<USER>/Desktop/66/lib/services/offline_data_service.dart", "hash": "2cb0167d2c9fb16e06920e968f194b44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/video_rvfc.dart", "hash": "9bd5317dcb318d2a314ef885a62bb243"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/url_launcher_string.dart", "hash": "27e6c510107a34001ef90f889281633e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "00dfe436d7f3546993ad86cc4f9ff655"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/axis_chart/axis_chart_extensions.dart", "hash": "3d2796b459c4d34219ea679827e92e5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/macs/cmac.dart", "hash": "ee4ce4a2c979a2ddf7bc31f42ba327dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/digest.dart", "hash": "e660b23c1ae29f74711126be6e560bf2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "d3b949a1e7578291493af5fd28846314"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/signer.dart", "hash": "83b3dc31517e191dc194852894ba25bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_storage/web_storage_manager.dart", "hash": "4e9fd8c95087cdab79552a1e8a896170"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha512t.dart", "hash": "9cbd64b569333e314236ab20ed1f6ef6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "ef24f0630061f35a282b177d372c00d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/http_cookie_same_site_policy.g.dart", "hash": "41b92d4b0cfbfa56e7369406f09af1bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_map.dart", "hash": "56bb06e8c5f7f7892ae9eb352dd91f9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/signer.dart", "hash": "5936497da2572436ae1351e736720360"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "a29f0df228136549b7364fcae4093031"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/main.dart", "hash": "e327670f86c16c411067b29745b05143"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/courses_page_new.dart", "hash": "f0a2f9c3e748722800da0beb65943525"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/base.dart", "hash": "34d65aad713399e0e95c6d52aea92d88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/course_videos_page.dart", "hash": "d857cd86f02b8185b723dfb8fc801799"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/dom.dart", "hash": "7f54e5ba0047e40abc9ab825d4e1c116"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "dd109d67b92b9fbe6e0051f0c890c903"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/console_message_level.dart", "hash": "18ec78912a0c60918a591a0cabc1f3a1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "2cf5ffb71954128b5e80f17a36bcde43"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "796af05466fbe319d5fc699b982ded0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/lib/video_player.dart", "hash": "7b89c8047f48699ec1efcfc089d321f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_resource_response.g.dart", "hash": "ee494f59324e418e07290349267a5667"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_webview/platform_headless_in_app_webview.dart", "hash": "631a7621caa5bb5e825a62ef9b6c6c4f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "2553e163ea84c7207282c18b5d9e14c1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "d0ab7f5e11e48788c09b0d28a0376d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/retrieve_type.dart", "hash": "550bfd92eddfc12d28a028ef44f9cedd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/webview_environment/main.dart", "hash": "569a25ba928c24269f7d11f316921a43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_resource_error.dart", "hash": "17ec36d514b848262c07a6146bfaf79c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/listen.dart", "hash": "4990e198f887619ece65c59a3de67869"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/staff_name_animation_widget.dart", "hash": "6e79f697cbec79259d093c0521cce8c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/clock.dart", "hash": "84ad21db5ba97deb809b65697546e39c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_browser/in_app_browser_settings.dart", "hash": "ce18872da724910b9ec202331f4663ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/default_extension_map.dart", "hash": "fe2df60ed5b05e922df2ee9fef5cf5d9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "3405e08e614528c3c17afc561d056964"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "52beedf1f39de08817236aaa2a8d28c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/from_callable.dart", "hash": "b05a68b737792aa52eaaa4d3e093bb63"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "8e870f9527626d34dc675b9e28edce85"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "bf50f61746b9744a0e2d45a88815288f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/fetch_request_password_credential.g.dart", "hash": "dfb7aa796ca2713002605377776c790b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/media_source.dart", "hash": "614adbb6ebd3754dd951c5e13c920257"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "f6d18a38c0986111a3d297424ed6fbcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/in_app_localhost_server.dart", "hash": "4ba212bcc090df772adba6d0798a7c3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/cookie.g.dart", "hash": "fcb7e310a440da86b2bffafbe332b3b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/platform_navigation_delegate_creation_params.dart", "hash": "9ffc6ec5d7ca9434ae8fd53a12cde3b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/utils/utils.dart", "hash": "6c479e0fd2351de96aa7368a1bf8f8ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_link.dart", "hash": "92be3b74ebf2b10ee5852ddbbc825971"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "2c5021ff8faa0330f66b1c501e8d4b22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/selector.dart", "hash": "6a72a2ba15880cab1e1d9a28a94f1a2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/rc2_parameters.dart", "hash": "30cd153e6972aee5f5f3b04cac7383d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/method_channel/method_channel_permission_handler.dart", "hash": "219013e8bbe8cf19fde2d3520067ba39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_advanced_segment-3.1.0/lib/flutter_advanced_segment.dart", "hash": "13bc647559ee09c43feb90c3aa6b80c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/pdf_configuration.dart", "hash": "348c56109800f9f6d7106e241c4e2e06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_settings-5.2.0/LICENSE", "hash": "553c00acfa1034d1d77d2d5934069036"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/rc2_engine.dart", "hash": "25f9d9a63608d55e043f713f8befe7f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/timestamp.dart", "hash": "bca54a6e3c0b80a2300ab9ae4e9db4e9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "b692d4a68a086507a66243761c3d21a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/cbc.dart", "hash": "f99e630fbf8e81649ca291c95c896add"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/renderer_priority_policy.dart", "hash": "5ef65e27576d1688bd7046741acae3dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/async_selector.dart", "hash": "c050fb9d5c851547735cf2c46d8b6288"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "0db5f597f1cc6570937e6c88511af3a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/push_api.dart", "hash": "c4a77ece416f851e2b69b7a57136bf4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/in_app_browser/main.dart", "hash": "2c38233ae4e3d16c537817dedbdf1aca"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "9645e1d88d63387bb98a35849f4cbe53"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/search.dart", "hash": "66a927b3f610db5ff8c77a6ba3ccee0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/constants.dart", "hash": "1ec635f2db97328558affe7a0c49fdeb"}, {"path": "/Users/<USER>/Desktop/66/.dart_tool/package_config.json", "hash": "a4f8af21c570c948d976c1453290e388"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/fitness_stats_widget.dart", "hash": "9d6ea34890814478281ac862f3c669ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/secure_payment_confirmation.dart", "hash": "ff010ada1c7b3a396c3bb39b067fb53d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/factory.dart", "hash": "63fa9307c55c93f4fde0e682e7da6503"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_rendering_quality.dart", "hash": "baf99661afe6681c97ae398f52447ec3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_webview/android/main.dart", "hash": "c30b75b4d34f9489824b72586d116ca2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/empty.dart", "hash": "ac4e4c808dab498eb2d5c7f813a5006b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_duplex_mode.dart", "hash": "558212c07d4f1de442b63b91b794a668"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "d3de616e525e730c7b7e3beb57930993"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/mime_converter.dart", "hash": "601a4561a6a4b9a0f99cdc39dbb67c0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/blake2b.dart", "hash": "8571b58dd534486695b789e2aa75a7a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp112r2.dart", "hash": "ae864bde681355d21277a204953c8060"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "deedcf7ee9b4e76191202e61654f9dcb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "74a89d22aa9211b486963d7cae895aab"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "501bafdb6d3784f18f395d40dfa73cd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_message/web_message.dart", "hash": "29bb3e59a7f9457f29e670dd5bfa2420"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/utils/map_utils.dart", "hash": "8031eb9ace7772162d05b8dbe89a6ec5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "6987c3474a94dd1c4ff8f8540212f16b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "d7a6c07c0b77c6d7e5f71ff3d28b86bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1teletextstring.dart", "hash": "57a7e11994ebd3d945aef5481188bea1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/gostr3410_2001_cryptopro_b.dart", "hash": "1047083da015c385f105aba778621fa2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "1ed34d373b037c1696e90bf7e4f249ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/never.dart", "hash": "238c701652f1f5ad9ba928994a96e608"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/tracing_category.dart", "hash": "f661e9f5800c67df78ea5891c2e783bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_protection_space.g.dart", "hash": "840f3daa81d58ee72a216a1cefb4afc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/window_features.dart", "hash": "63c2fe929d70bdb4021138c566ed27e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.0/lib/src/method_channel/method_channel_firebase_analytics.dart", "hash": "96eac7c08ada2c24bb39c84f2b4cb169"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/asn1_tags.dart", "hash": "52b3957b97a8395768c49a8e007d55f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_y.dart", "hash": "853e7e8b3898f3c0055ae0ae1630e229"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/LICENSE", "hash": "d4d8ea93163650186c3e613967015112"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_process_global_config.dart", "hash": "cdceb7841de1da2722e1834f94e7c8ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/request_focus_node_href_result.dart", "hash": "bb992be2a3ede3fff072bfc22ffd8ae1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/android_options.dart", "hash": "2d04b343ac3e272959ffa40b7b9d782c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/mime.dart", "hash": "90fb56de843f1f933c2ba8ec945fa06f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "ed28f6ca17f72062078193cc8053f1bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/notification_play_state.dart", "hash": "2ba84178719d1dae120fe8e1144bc1e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib/src/link.dart", "hash": "f40d1d82dd5063d51b2e915133377e7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/assets/t_rex_runner/t-rex.css", "hash": "5a8d0222407e388155d7d1395a75d5b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/chrome_safari_browser_secondary_toolbar.dart", "hash": "4f804a6ab13f6aa80f88a69abb9a551f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/window.dart", "hash": "b081e406a9e3448ff172ab7d21f31f7a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "d942bc7ece253c7918e1f60d35e233b0"}, {"path": "/Users/<USER>/Desktop/66/lib/services/daily_streak_service.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/flat_map.dart", "hash": "115640739fe47a728c4b1c3a4b4c3506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/action_mode_menu_item.g.dart", "hash": "8c9a2644fee27fef09ba14e092c3460c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "25b96e83b1368abc11d4aeae19e9f398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/src/in_app_webview/_static_channel.dart", "hash": "60c5ec17a112ca04d333047818a7a7a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/mediacapture_transform.dart", "hash": "c7cf83a1db30abb62d2f6f9c10d30c91"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/time.dart", "hash": "872d879ea43b6b56c6feb519cc12d5a9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "b56cf23d49289ed9b2579fdc74f99c98"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "951bd729c13e8dd03a7f4edd8b10c06d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "1c7764fa08241a44711301c74fb658df"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "256d1c386e48e198e2e0a04345221477"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/pie_chart/pie_chart_renderer.dart", "hash": "31b99a7a3a9fd6567d08c913285937d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/notification_button.dart", "hash": "b57ac80d3f2933e30a3ed4354109f401"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/LICENSE", "hash": "8f29b74ba6fa81721ca1cd98cd39ae4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/fetch_request_credential_default.dart", "hash": "142de71ff72319838a357b069ea91f74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/always_alive.dart", "hash": "ae4469331dace367e6fb978dd7b7737e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/lib/wakelock_plus.dart", "hash": "439e4141d92786fee43476f55f6b7728"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/chrome_safari_action_button.g.dart", "hash": "d4bc268fe8e56f17dbed87b4ee91fb7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/streams.dart", "hash": "25a929555febc01ae405a334b5ab9ce1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/src/messages.g.dart", "hash": "814815839a4b6d2924a5a8661780b0cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/over_scroll_mode.dart", "hash": "908a6bfc348e123f1c92d0c9735ef2fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/line_chart/line_chart.dart", "hash": "b9199dbb62d91ca502cae0c86f2ad92f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/axis_chart/side_titles/side_titles_flex.dart", "hash": "74c234daeb81d56ee7596c93001202b9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/button.dart", "hash": "d7a239f8b80f844857527c2012e4fa1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "74708cb40b7b102b8e65ae54a0b644be"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "d3abf203392ec29c7ebbda6b41360d2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/webview_credential.dart", "hash": "35c15d26a69dec84849450a180b2ac7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "build/web/manifest.json", "hash": "e62d8075ef29fde5fef1bd8b23bbe8d8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/card.dart", "hash": "90d9d45eef80ac53b194a71da4e10975"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/enumerators/notification_source.dart", "hash": "f63f69f60fb10e1e1a08703256aae02b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/constants.dart", "hash": "195aceb9dfe0dacbf39711b8622ce2b4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "ae85856265742b6237ed0cb67c4364af"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "e7b2de136a99cf5253477d4fb4138394"}, {"path": "build/web/splash/img/dark-3x.png", "hash": "83bc7b765cca0d749dee3db9e14a0405"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/errors.dart", "hash": "2a7dd605fd24026f238835990b2af51c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/utils/utils.dart", "hash": "cfa114ac14f2ea544dbeacaa24b2a9f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs8/asn1_encrypted_private_key_info.dart", "hash": "df97742fe1045d91a738194f3f00fa48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_prompt_response_action.g.dart", "hash": "21e19920d1fdcb1aafa8162e198743de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/user_script.dart", "hash": "fc69464f28a398a4238ec899ba861ca6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/aes.dart", "hash": "2e1c42090ed6f3a2feddd00729eb467e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/compress_format.dart", "hash": "21f646c1feb693f9145a3feae198a980"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/api.dart", "hash": "9c1b891920008b53059d7791d88f7d4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp160k1.dart", "hash": "9c471f292c3ffd4fd6519b21ccc9ff07"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp224k1.dart", "hash": "a6159ee3c90b2196e3fab3dc5b1b51f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/backpressure.dart", "hash": "33135edc8fab501ab562a08b8322d832"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/service_status.dart", "hash": "5072fb1450640d8f46605ff67dafa147"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "a69e90f683dddaf61ae8d7f094219026"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_request.g.dart", "hash": "04eb4bd9acb3ebffcfb5d4d8e40fee35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "build/web/assets/shaders/ink_sparkle.frag", "hash": "ecc85a2e95f5e9f53123dcaf8cb9b6ce"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "d8060c05b658b8065bc0bfdff6e4f229"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_bmp_string.dart", "hash": "dddc657850282e13df5f4944bd879f97"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "f663757bacdc28f2692b30a293d75146"}, {"path": "/Users/<USER>/Desktop/66/build/web/flutter_bootstrap.js", "hash": "5dd8ca6fc07e76e7500635a6056496ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ui_image.g.dart", "hash": "78a847b953a4daa91afcdc407021e29f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_h.dart", "hash": "fcfe1d3dbdb081cdeca153aebf6667ab"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "3d27bed38f1893769396b5d23f94f15e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "8383986e94be1a258a59af29b9217876"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/over_scroll_mode.g.dart", "hash": "eef42fea87b169a2efe097f4f03384f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_draw_buffers.dart", "hash": "eb114ec5ef68168fddc81eca33e321f4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "547eac441130505674f44bf786aee606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/flutter_secure_storage_platform_interface.dart", "hash": "8dac3815609f98dfefa968bc2ea4a408"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/src/encoding_parser.dart", "hash": "109eeb63e43422d207e9ad771c2ab623"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_storage/ios/main.dart", "hash": "0e850fb5da721066e0599ec36bd9d451"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/attributed_string.dart", "hash": "7e03e873f4e45dbfb21abd2188b1471d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/home_page.dart", "hash": "9f590b7ad97658f267bb0e4493f0b12d"}, {"path": "/Users/<USER>/Desktop/66/lib/services/single_session_auth_service.dart", "hash": "b48d962f1c7a1aa111ff88bcb96755bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v4.dart", "hash": "916cd94d810ea5b86f0cdc685dc38001"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/pie_chart/pie_chart_data.dart", "hash": "37418c6511526f76b2a427915fc39f04"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "22ad3e3602e0fc7a63682e56a5aeaac0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/print_job/platform_print_job_controller.dart", "hash": "bb0a064333a758ac73ea4c91deb4cfbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_s.dart", "hash": "2d79382537f3ba898ab7a80cd0fbf0ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/lib/src/platform_util.dart", "hash": "2f6b1ecb411e600c9e7da32840575598"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/modal_transition_style.g.dart", "hash": "e92e627f113f63af3f7a85f03be9b914"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "32b222420709e8e40d12f6ea9fc0041e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/context_menu/context_menu_item.dart", "hash": "78fe0f7eefa30406b5d7dcb460f6a0e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "7018ea64a9aab18f27a10711285d7573"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "cb45dd3f32378f0acf6b8a514cdc6084"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_legacy.dart", "hash": "4144d8b8e1cae585ab9f01406b3e1f75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/linux_options.dart", "hash": "26c4f0c369b83e53900ac87bf7e0dcff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/firebase_options.dart", "hash": "3776c53b4b5546b121422c8c43cbcd7f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "a46ede2164234d7371852e8f57865dd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/selector.dart", "hash": "c771f26d18f9897af0e13e3a2c83d5ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/private_key_parameter.dart", "hash": "132622d54cd53aec2b21591e6b5a90b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_request.dart", "hash": "a2d6a9576bf092dc7c16e2b66f533fef"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "59cca02e92c0ff79aac0c54c50e3bd2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/css_link_html_tag_attributes.dart", "hash": "2e4c23f2902bac3c723c51f5bacab77d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_last.dart", "hash": "355616c9fb00a5e0ec803fffa8f33eff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/requestidlecallback.dart", "hash": "4082f30e5cc474e4f38820b93f30ef3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_resource_error.g.dart", "hash": "d87a30c30d1914afa845119633e56e83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_source.dart", "hash": "da5faa2d91b7029347d1a39bc0060cb2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "82604e7dbb83dc8f66f5ec9d0962378b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/chrome_safari_browser_settings.g.dart", "hash": "b100765aba430261d18a2eb75aa51597"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "d9dd226ec96aec60f125c0f1f8d00344"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_view_transitions_2.dart", "hash": "fa4a3e6a968f48ffbb520a01d20a34d4"}, {"path": "/Users/<USER>/Desktop/66/lib/services/app_lifecycle_manager.dart", "hash": "046644aa0deef7d47c104944feb4d99a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/fetch_request_federated_credential.dart", "hash": "dccd404db664ff53bac9155a91c2a3dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/riverpod.dart", "hash": "9518a1e0696846221033c0434d777377"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "7088cc45b21c93be6b42dc748fc3a29a"}, {"path": "build/web/icons/Icon-512.png", "hash": "ce3d4a1216bdefdc14a07019b7fba41e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/form_resubmission_action.g.dart", "hash": "a04f13672f47de1cd8bea4110f4d04a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/base_chart/base_chart_painter.dart", "hash": "5f008cd9cbe63cd968d0781806594bf3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/_network_image_web.dart", "hash": "9fe9727f9e8a429f5a682c695c4251dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/custom_scheme_response.dart", "hash": "114f99d439c77cebde79dd3e949ef14c"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/more_page.dart", "hash": "9ac6863c0699f2abb083e0318df89ca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/print_job/main.dart", "hash": "4f4ef9eeb6a2335405d36e852c482347"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/prewarming_token.dart", "hash": "74c24c7dc0b1197b125ba73b070fdb24"}, {"path": "build/web/canvaskit/skwasm.wasm", "hash": "f0dfd99007f989368db17c9abeed5a49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/lib/src/web_impl/js_wakelock.dart", "hash": "639ea7bbdc0f493779b9ce0eda86633c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/mac.dart", "hash": "de2ea3d1fa63703972769a6dd0c0bce4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "3e1bb909dcd21ccd8bdc03ba57bf02b2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "c8564aa311746f4047cd02e26ff4df75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/x509_certificate/x509_certificate.dart", "hash": "bb94abdbb669ab9c7a328e2ad392f089"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/radar_chart/radar_chart_data.dart", "hash": "97cfd5c2e1149bacf08bc49384bde827"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/scatter_chart/scatter_chart_renderer.dart", "hash": "2ac4d1bc22a9e8eea30b279144d2898a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/lib/src/model/web_browser_info.dart", "hash": "9e887cddbdf6c6c7c650a995832b127f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_resource_request.dart", "hash": "301b0bceed3c38727a915100b7a5180a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/LICENSE", "hash": "7d8c2c0f16b6e8c986866f4e590cea06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/webview_environment/webview_environment_settings.dart", "hash": "bcf6aeba9effe5426f3b286d0babf371"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/helpers/extensions.dart", "hash": "54974b54397f63e417b9ffa24e4d6922"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp128r1.dart", "hash": "bb0e9102c94c06da15d291c296ae6d97"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "006c00513de6bd421565ec6ffd776337"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/theme_toggle_button.dart", "hash": "a56e7879d78052f4d5a46f61ee007646"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_devtools_extension_data.dart", "hash": "3f47c1f73c7a4541f98163b83d056456"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/primitives/asn1_sequence.dart", "hash": "93ced0a5981ad9d669e7ff36f193f598"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/request_image_ref_result.g.dart", "hash": "e00af4bbdf845c7630b81c0cffd0e35b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/vertical_scrollbar_position.dart", "hash": "5aa3d92cf1497f277e0ca3ae46d09e37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/media_capture_state.dart", "hash": "4eaf4e62f9c52bf47670be240ed0287c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/auto_dispose.dart", "hash": "d2e52f81da2329303a3f9d4b369c3320"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/secp160r1.dart", "hash": "802c6118f631a36eab3448fe69584b97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/pairwise.dart", "hash": "e8e03ace330da6d410583717e7e5f681"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/Users/<USER>/Desktop/66/lib/utils/video_overlay_helper.dart", "hash": "74ac00932fb48b5a5f468c561fb28504"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/min_max.dart", "hash": "50c5f00339854085c2f637109c4166f3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/Users/<USER>/Desktop/66/lib/services/performance_monitor_service.dart", "hash": "2e445f5eace11c829d9a00a9c80c9059"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/devtool.dart", "hash": "2d7d80b5c908559a133f8729b6e755c0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "8b15d222f5742b46bf55a4ef4cbfd6e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/result.dart", "hash": "1325fce32c39a3792e3eeab612f942f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "4372cb3b63b820aff3fe67061bba3f9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/oes_standard_derivatives.dart", "hash": "44676c94663b8ff333fb9104b594ea02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/src/enums.dart", "hash": "1c71712af9ddaeb93ab542740d6235fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/des_parameters.dart", "hash": "39880bf6cfebdca852998c5a638adfa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/oes_fbo_render_mipmap.dart", "hash": "1c661453d0be382d5fee4fc5863cb953"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/user_script_injection_time.dart", "hash": "75a55ede3278b27c5e48d55c93d1f5f6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "9e353a749332f6cfdbe6f0d07ff17f5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/pdf_configuration.g.dart", "hash": "06d089bddd9423c449b5185b198af976"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "2539eaeb4e2f2f69f678fd850c2332e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/utils/canvas_wrapper.dart", "hash": "a2d72bf958dab348c9ba7c05e81a03dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1null.dart", "hash": "db5ad11249ecd359da07740652abc5cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/permission_response_action.g.dart", "hash": "41261fba6031be0fd6e66044235235fa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "f8275b74f8f83272b8a8d1a79d5b2253"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/optimized_splash_screen.dart", "hash": "e8f606db66b6f2064e72a0b963994639"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE", "hash": "9633ac2bb6bd16fe5066b9905b6f0d1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/sha512.dart", "hash": "525412ef37eb7978b3b20aa955b9dfa2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "23f5fb6033bd02c94d263d1ed41fb90e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/stream_cipher.dart", "hash": "625e237fb6c6be7fbd30f31b9c6793fc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "a108c1a02c56f9162ede59a7c30ed41d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "16903e1f0bc6b66d30a5804b7ae71fe5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "b139a58dace0b9d9a07a3523ed72ced5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/navigation_response.dart", "hash": "07e3cb222c3d561e372c1e0898d805d8"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/logout_button.dart", "hash": "3369f20ef5cd90f83f76415da86947ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/mediacapture_fromelement.dart", "hash": "456edf48718a9d59a2fa9b7e937a986e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_stat.dart", "hash": "1b430815bdc7bab3a240f27e745f8977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/login_request.g.dart", "hash": "19374fb6d2f512109bacc67aedfe661c"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/profile_avatar_enhanced.dart", "hash": "ac7e7e73a6f435bdbc25024ff88347f4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "becd419f96efe14f36f18a8c8adc82cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/in_app_webview_hit_test_result_type.dart", "hash": "a6971543403df8038df779f6fa590fce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/platform_chrome_safari_browser.dart", "hash": "55bc00b48cf1f7b375f8860d912d326e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "55f7619e20765836d6d1c7001cb297fc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "f30e48d0892af0c99b54816673cff9ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/user_preferred_content_mode.g.dart", "hash": "2df1527d39bc64f5056b67e0b535ef56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/mime_type_resolver.dart", "hash": "e96fb06ec6e2c706ca24c80b71a382ed"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d33374c0857b9ee8927c22a5d269de9b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "1bdb47a9af4b0a5d759937da8ff04db0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/axis_chart/axis_chart_helper.dart", "hash": "cba151c9b40d4a202dc8c525b31fce48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/image_picker_for_web.dart", "hash": "ae3b209338ec8ab5574711dedbdfc648"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/multi_image_picker_options.dart", "hash": "5ad1b4844df9d51e4c957f292d696471"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "0fbec63144acf1cb9e5d3a3d462e244b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/indexeddb.dart", "hash": "69a74463ae4c417d0084353514546c28"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "e4c4603e78131a8bc950a8029d624a76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_v.dart", "hash": "4cb87d15a1cc8c482587425775418f04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int32.dart", "hash": "f6b2a03b8f3554a6b37f151f6a561fe9"}, {"path": "/Users/<USER>/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/LICENSE", "hash": "22751c3fbc8668904249121c47d419af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/custom_scheme_registration.dart", "hash": "57b40420d7d16c26e44f0bd05db84c56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/user_script_injection_time.g.dart", "hash": "c2a3ac665cf99816d4bc5d153dceb6f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/http_authentication_challenge.g.dart", "hash": "c3c83328bb7d5cbd8be668369c097ba1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/print_job/main.dart", "hash": "f21fff31cf981741ba134dac8d1f2ec5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "3cb88cf9e4198e0d510b78aa005aa597"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "e01f6851d87ad96cbdafcbfd282517e6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "484329e20b76c279413a7d6dc78b3223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_depth_texture.dart", "hash": "af699860aa1d81640ccd60196bddadab"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "53cf0d76bfd70bfdc7e2edb4a18327f4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "37f181e3096dc69dc408bf7d07fcd39a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/LICENSE", "hash": "e6465045b7ce261fdfe7f17c9bff6249"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "b815d11a718e0a4d6dec5341e2af4c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_float_blend.dart", "hash": "1347d790ca01704ce589d0e001b9f24f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/eax.dart", "hash": "db3d47bbd056cc10e133a00439925906"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "798f76b8076951e542aad4221a45d480"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/signers/pss_signer.dart", "hash": "7b256fd525aca27690ce2f2621caf3bd"}, {"path": "/Users/<USER>/Desktop/66/web/splash/img/dark-2x.png", "hash": "fe25924a52b5bfc49b6587ffe0d54f53"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/meta_tag.dart", "hash": "2497b7ae34feff9ce40b4b30ecac8391"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "9a31689295b300aa8ab12d29fb8853ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/in_app_browser/android/in_app_browser_options.dart", "hash": "e00f6dc4f78dad01b61bb977e87d3633"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_if_empty.dart", "hash": "f1236a5e582b3794b3fb2302d7297451"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/base/line.dart", "hash": "6ee5fd030044f9ec87835e34b09f7755"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/http_auth_response_action.dart", "hash": "084f8d9738d71068d1792a969aabdb41"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "a9e3af96f170745db1c281777cb6bda9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/fido.dart", "hash": "f9c1699509f8a9a0ebb70f224f99cf55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/awesome_notifications.dart", "hash": "8780fddf33a12b0a5fb0874099d0730b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "bd3f0349089d88d3cd79ffed23e9163b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/activity_button.g.dart", "hash": "8e2d886af4902a9eb3ebcda34f4624a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/navigation_decision.dart", "hash": "6f3bbbb0aa2a6e5ac518d26192918a12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_transitions_2.dart", "hash": "1674cc51f019878df5a2998c7661bcf0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/debounce.dart", "hash": "1aea282ab07e82afe7a564125110e1fc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "75fa80ab7762b14e35b11b93da96d4a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/utils/bitmap_utils.dart", "hash": "98c9d85569a3f85dd73df245cc0bd301"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/web_authentication_session/web_authenticate_session.dart", "hash": "110ff6bd96f0dfdd670b4a941b8919e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/src/secure_random.dart", "hash": "9e172299072e646efdc499bef0447fac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system.dart", "hash": "a0432b1db3ddabe8c3edb6f542c9ef48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/xhr.dart", "hash": "4efd485a39c822e8c66062c390eacf7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/signers/ecdsa_signer.dart", "hash": "04df49115f42e6aba6c7182784ef0247"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib/url_launcher_web.dart", "hash": "3f6e143a371ae3ea26ccae00a723a057"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "05d4aeae6031730c6aa412a128f67448"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/method_channel/method_channel_firebase.dart", "hash": "a3239e1caa780c64918575ebdd5dd4cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/package_info_data.dart", "hash": "e1e3a7882488820f09a44a49d8efed8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_highlight_api.dart", "hash": "d7811ad2469eaae161434b3d6d29d375"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "d2694042e337ac1f2d99602c25be195a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "fc0b4ef021be19542435a86743d8de7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "830b9f37313c1b493247c6e7f5f79481"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/bar_chart/bar_chart.dart", "hash": "39d14075be6f21f66bbda6f9e095c479"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid_value.dart", "hash": "6edd9b910f41e28e574e1c5308ef8b74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/secure_random.dart", "hash": "22aeb6b715e318e5a73e1137d665d01c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/src/pkg_web_tweaks.dart", "hash": "4b4272c5cf042fa07b2eb1d12cc5f920"}, {"path": "/Users/<USER>/flutter/packages/flutter_web_plugins/lib/flutter_web_plugins.dart", "hash": "7fc713248402b1a9daf4c23bedd090e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/src/api/padded_block_cipher.dart", "hash": "54199c9259c98b0a291f90a10e4a89cd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "0c402ad9ba3f3e4d7f45f24b27447ec2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_contain.dart", "hash": "d97ab713e0f59c5223dfaa0bc527db01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay.dart", "hash": "46133866c09984f60ac2731cf9094a27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/encrypted_media.dart", "hash": "c53973182da208da61ea4f0ffd71df8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/web_authentication_session/web_authenticate_session_settings.g.dart", "hash": "2ba9ecd5ef340b79c6a1e6ff86dd6bb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image_transformers.dart", "hash": "10404d098f485bca549850751b3e93b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/scrollview_content_inset_adjustment_behavior.dart", "hash": "04113a331a7346a6bae4e00ca120793b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/webview_package_info.dart", "hash": "dd75f552909cc67596f31f06f727a49b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/whirlpool.dart", "hash": "09dc184f9ccb3a851aa34deb56ecfd0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "7bdfcadf7dd131e95092d30909e5b11f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "cd7cadd0efa83f26d401a14e53964fd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/digests/ripemd256.dart", "hash": "ba6e74d916b71ed007cb2126f50074e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/force_dark.dart", "hash": "25971190849eb0f5963cf40e439e218b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/renderer_priority.dart", "hash": "16da949874517dc52c1efcf715f7cf88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/referrer_policy.dart", "hash": "1239848c03a1587a30731bd89231ddb6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/prewarming_token.g.dart", "hash": "5af049eed441f9e8d55c7e6908f4eac8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/inherited_provider.dart", "hash": "dd618a65e1f3400d8224fedb42a1881b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/decoder.dart", "hash": "e6069a6342a49cdb410fbccfbe4e8557"}, {"path": "/Users/<USER>/Desktop/66/web/splash/img/light-4x.png", "hash": "7520625a040b816f2d8ec86ef8b5af52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "912b76b3e4d1ccf340ee3d2e911dfd28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/block/modes/ctr.dart", "hash": "154d573def33d0ada1c6dfc13b6a8feb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "247fd4320e1e277acc190092bf6d35ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/navigation_action_policy.g.dart", "hash": "606549c75cf8ad6cb31020513553c6b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/custom_tabs_post_message_result_type.g.dart", "hash": "b87f8a2100a54d86efe9d04156c2dd03"}, {"path": "/Users/<USER>/Desktop/66/lib/utils/animations.dart", "hash": "b6cc3cd259eacf57f08229be23ae4ad3"}, {"path": "build/web/splash/img/light-3x.png", "hash": "83bc7b765cca0d749dee3db9e14a0405"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/clipboard_apis.dart", "hash": "30e5d39c45acc953b5bdcce6baed9def"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/legacy_api.dart", "hash": "e62a8f39ad332b5e313b0be97f2d280f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "b6e992b1127f8376358e27027ea7a2ff"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/profile_page.dart", "hash": "49a7f5826967693d51c39fe7f315c5ee"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "04f538d5fc784c89c867253889767be4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "1363e5e6d5efab4bae027262eff73765"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose_family.dart", "hash": "3b32647556f88ddd6d625ddc58c7691e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/oes_draw_buffers_indexed.dart", "hash": "16101e10b183695e9eab803790cc4f19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/login_request.dart", "hash": "ef8acf85d600e8f5974c1e902bae559d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/src/mime_type.dart", "hash": "f5e211d8beeb6fe549de90fbc48a4a35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asymmetric/api.dart", "hash": "c62bc39c4eb449cdc6eca90e1ab97662"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/console.dart", "hash": "54b083c045385cbe9db78b82c60a4d93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/window_style_mask.dart", "hash": "a815b55f21a1d671a950fd4540d375ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/pointycastle.dart", "hash": "456516d7456834b1833931118899104f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "e79db1a382e61436ed81f9f47dc06d7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/svg.dart", "hash": "8cd036f452e07f77feeb099c5ca20538"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database_mixin.dart", "hash": "5e9d885bc066ae16bcca5bf065c9d51f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/platform_in_app_localhost_server.dart", "hash": "41a9e25ae2db7f5d3e43cb5e04a26e9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/http_auth_response.g.dart", "hash": "db8ea4f37a29fb3ffad9461267057579"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/number_parser_base.dart", "hash": "39348131fc86fb08a42dd6b2d1b16bf0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "8ae04de7c196b60c50174800d036642f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider.dart", "hash": "332fc1055d849f61ff8cb6ab6a919d1a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "577ec098e9f6651d7704fad48b4dd44a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/test/test_flutter_secure_storage_platform.dart", "hash": "362bf1b65ae84f1129622a8814a50aad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/brainpoolp224t1.dart", "hash": "8313696f70c1a0137e43aacbbf23a76f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "83df4f6e4084a06a4f98c27a524cc505"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "4d673eddc0bd2289539b66a92faae868"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/extensions/bar_chart_data_extension.dart", "hash": "81c45842aae33b39d2fa3f467408ab49"}, {"path": "/Users/<USER>/Desktop/66/lib/utils/platform_storage_web.dart", "hash": "80686438cf96dccb108cb609fef3d46d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/src/token_kind.dart", "hash": "4b721bbf0c0f68e346e09e254b6b8d5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/x509/asn1_algorithm_identifier.dart", "hash": "2e28d4e67e9696c085e54b54aef9394d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "a2f376b739fa28d7a71312ecf31d6465"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "8f24c8ed1935c6f08997d0b9acb5bf38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider.dart", "hash": "d28de61067df9bc3d509be84deec1140"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "ba2f8adc4e6c096b09aac919580fffee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/encrypt.dart", "hash": "8b5fb284932f8980730cced7197be312"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webcodecs_av1_codec_registration.dart", "hash": "c1eba6d2efaaa33fde653496c90cf15a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE", "hash": "c23f3b290b75c80a3b2be36e880f5f2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_conditional.dart", "hash": "3e06f0d1bccdf76baf4f4e0fb4868c84"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "019f7b771f1865632d5a36c9e74296db"}, {"path": "build/web/splash/img/light-2x.png", "hash": "fe25924a52b5bfc49b6587ffe0d54f53"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_history_item.g.dart", "hash": "3a50432431a7217502d7b1c6d50c2fa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/media_playback_quality.dart", "hash": "6005946ba650c618c2eace5c1f999212"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/transformers.dart", "hash": "5d2971806de340d9e970e21af445505b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/url_launcher_platform.dart", "hash": "0321281951240b7522f9b85dc24cb938"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "3fa7a3bafbab98c305119475eb004a06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/webgl_compressed_texture_astc.dart", "hash": "6f4f3b33b7bc8ecd9ead21959e169f7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/repeat.dart", "hash": "57ef315bc7f35da7e489915ef8572118"}, {"path": "/Users/<USER>/Desktop/66/lib/utils/image_utils.dart", "hash": "8489619a93da3e3c13bfae5f94c392ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_selection_type.dart", "hash": "dd685f95d5588b8d81d3913338ab9cd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/LICENSE", "hash": "fcc4d991b068e4103c4ef152baf65fb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/oes_element_index_uint.dart", "hash": "f6aa572e7febf8e0269780f1ef8928c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/src/interop/package_web_tweaks.dart", "hash": "1fee2c8e5c81d253bf4e26a4d0cf505f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "307c2ee6ebc77b9995c2799e8e0bed81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/LICENSE", "hash": "53d71cd0b0021b18407a0b37287e730f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a06bb87266e0bac30a263d7182aaf68c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "ad631d7cd122efc4862c1c084fbde716"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/type_conversion.dart", "hash": "032c93433e86ca78b8bb93e654c620e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/orientation_sensor.dart", "hash": "7c2fdebd830f06bff067e79104a025b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_stream.dart", "hash": "3473bd2c623a639ff8cc439276a3825f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/lib/platform_interface/share_plus_platform.dart", "hash": "41a8a6c9daee486dedb0afb27b7766f8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "93c17b2980fc5498f3ba266f24c6b93b"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/video_player_overlay.dart", "hash": "8aaef98168f717ee9b2f92f83e930adc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "3ee6304161ca2993b303a8074557fe66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/string_stack.dart", "hash": "aa27dfc54687394062db977707839be5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "dcb5ce635282f4390eca8dcb73737991"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "9802442b82d3be84abecae8d0a2c7bd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/remote_playback.dart", "hash": "eda773e90fd6e46f7443712a481a89a2"}, {"path": "/Users/<USER>/Desktop/66/lib/services/user_service_new.dart", "hash": "f140c2dd78c24f8d558602a1efe624c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/chrome_safari_browser/chrome_safari_browser_menu_item.g.dart", "hash": "eee595ba51c4ab038cf9d50475add619"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/window_type.dart", "hash": "2e30bf8a06b709a932cc98fb6757c0cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/proxy_controller.dart", "hash": "86af4f47e2204ec5cccc66a1ff0d47e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/regexp.dart", "hash": "10ca1bc893fd799f18a91afb7640ec26"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/offline_video_player_page.dart", "hash": "251cdf0b8cdbcafb6bb7c47d7d9ee60e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/filter_effects.dart", "hash": "3cd49043e01257e2a2bc66975e708b02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/pull_to_refresh/pull_to_refresh_controller.dart", "hash": "ce23edc88450bf5d35db59421a6069a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/value_listenable_provider.dart", "hash": "bbd255fe46712b372dfe3b99cb340068"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database.dart", "hash": "66f280c66f95d03902082cdd2b4255e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/platform_interface/platform_interface_firebase_plugin.dart", "hash": "07db573490cf88af2c2da7b393436779"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/framework.dart", "hash": "d63ca0c723f6a99572c806b4ec989036"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1parser.dart", "hash": "35289b444f8aee514f82056062db704e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_options.dart", "hash": "44005c1b9f4a2f37139637ce53b7bcc7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/helpers/lists.dart", "hash": "1c184e2a9a0ae3bab3e8ae215f5061ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/custom_tabs_share_state.dart", "hash": "1017d1b2f96e8fa580d7cc572ab08829"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/provider.dart", "hash": "7c0851720900806fa2a397a81c81875e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "27a4ea7d50fcfd776a5d69fce0cd26ad"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "2a374faf6587ee0a408c4097b5ed7a6e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "083722b0880e8e5981f9e33da11e449c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/media_playback_state.dart", "hash": "7191d9d6c3d6125b6676824263994cbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/src/types/scroll_position_change.dart", "hash": "56026b6e71a025773b8b6236d7708d92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/client_cert_response.g.dart", "hash": "5dd9b5491ab49fc9e29df77bcba4c170"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_advanced_segment-3.1.0/LICENSE", "hash": "0a777abcaf1ac6185364bf4eae303ba5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "782760e5709624f38ebac3b7c728a792"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/fetch_request.dart", "hash": "35a66aaa3ef7aa7bb7fe4ed1746cf198"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/object_identifiers_database.dart", "hash": "c86737c1cc4387293239e818fee5be74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ajax_request_ready_state.g.dart", "hash": "7ea81eca08839d2e4bd984344969829d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/activity_button.dart", "hash": "548d1fa6dd0508d1db9d0aa49ed483e0"}, {"path": "build/web/assets/NOTICES", "hash": "1fced92d9db198187041c24a6d091bde"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_until.dart", "hash": "85fcef4d360ca759563bbfbe7c8d5e8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/content_blocker_trigger_load_type.g.dart", "hash": "1124298c2b7aa2bfe63a2218d15b3298"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_prompt_response.g.dart", "hash": "2e610e14f0b1baaae271adfd4701014f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/screen_capture.dart", "hash": "a7ca311b68f6ea52b0980d9f502fb6d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_request_attribution.g.dart", "hash": "ca293dddc2975f2fa8c12af0120cca88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "5ac341d21fd38e1a3307100a5b3c3138"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/premium_header.dart", "hash": "89ee23eaced82c7a40919a2c958c6580"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/lib/device_info_plus_platform_interface.dart", "hash": "4b532e7a43e7a2a180a49c5869ec8ab4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/src/types.dart", "hash": "ce0d3155596e44df8dd0b376d8728971"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "40587a28640d3c90ad2e52fdfbcd7520"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/settings_page.dart", "hash": "8ff5d663ce746fe54daf68e4ac6fb874"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/lib/extension/color_extension.dart", "hash": "9e8e0ef8c6ff68d5abb73724cdbae361"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/chart/radar_chart/radar_chart_renderer.dart", "hash": "e5816d998b0af0e0c0bc16ee2dea0a4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/print_job_state.dart", "hash": "fca782d9bbbde57d7317c2f4a9c974a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/macs/cbc_block_cipher_mac.dart", "hash": "4818af41263ddf07c4f6462457ae28d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/change_notifier_provider.dart", "hash": "3ce0eeefa3058c1955fb1f435ce9928b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/lib/src/models/notification_interval.dart", "hash": "77512975c0da2103f885eee9b096bcd5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/src/intl/bidi.dart", "hash": "432ff5976b2e0c85f249933d757d0e5b"}, {"path": "/Users/<USER>/Desktop/66/lib/widgets/network_error_dialog.dart", "hash": "8d63274e74080ee3574318087c573df0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "9b1cee1f8aa8b638cad928232383b02b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_alert_response_action.dart", "hash": "c46c20f2761520d12ae3784094b98267"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/result.dart", "hash": "08e7cd384cfc0214e088945638139ce9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "044d6bef26a97ada1d56ff6fe9b7cc14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/modal_transition_style.dart", "hash": "13e9ce1e60efc48e4dde526faae38046"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/main.dart", "hash": "98daf8513a54cf4490d71b1bb2b7f602"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2/lib/src/extensions/fl_titles_data_extension.dart", "hash": "c0070ef856f5639fd65493502f207567"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "505d7dde41bffe17b69e52db6ab37d0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/trusted_web_activity_screen_orientation.dart", "hash": "568048a2469a29c9d5ca477096c89ca5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_font_loading.dart", "hash": "9f7ce6effb58ed1966c1b1be3afcc6d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/LICENSE", "hash": "815ca599c9df247a0c7f619bab123dad"}, {"path": "/Users/<USER>/flutter/packages/flutter_tools/lib/src/build_system/targets/web.dart", "hash": "f456cd6c24936cfaf782d1868e32d6c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file.dart", "hash": "d9343422c8a6829bd05698de67232591"}, {"path": "/Users/<USER>/Desktop/66/lib/models/course_category.dart", "hash": "fd0509f29850b2ca5fe9a9a2e53b2c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/web_animations_2.dart", "hash": "22b72e70978c2bbfb3b0c370a22b9282"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/css_cascade_6.dart", "hash": "1b34c2a0713b355a521927aabe0eb516"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/run_guarded.dart", "hash": "ddefd207562d7e33dc44d433e0848e1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4/lib/src/asn1objectidentifier.dart", "hash": "5506829f2564c63690458f8d031b85b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-3.1.2/LICENSE", "hash": "4da7522cdbae881974e8e3e922db74ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_many.dart", "hash": "1f04f05279660e26d85fff2f5dfec4c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/platform/platform.dart", "hash": "17488cbfc8b9ee2e6e5ba0229d7c21a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/data_detector_types.dart", "hash": "9407b96010b89bc5475f4ab4fb1c7d1f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "33ce088a133276cbfd4a33ec49bdcb62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/cached_network_image_platform_interface.dart", "hash": "d9ccb5a0c8dcf64361a257c101d0e719"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "555fcdeebbe6517cde1cdd95133cabd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/favicon.dart", "hash": "7f1d048c8f65ae20c21ce838b7b61058"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/download_progress.dart", "hash": "1e0f86acf6978afd1769e17506893606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/helpers/renames.dart", "hash": "a148766f1d7ee563c9581773c40b7641"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/url_protection_space_authentication_method.dart", "hash": "04b9783447848ddad4ad8e0f3191c0ac"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_p.dart", "hash": "af493bb7ab298cddebf04d46f7c5dc18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/x509_certificate/asn1_identifier.dart", "hash": "0aad3566e79f0081c63e2460ca46e378"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/base.dart", "hash": "b21a009902949ddc4ba80d607867fcb7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/lib/method_channel/method_channel_device_info.dart", "hash": "47692aa3b3506b6ac3f2639c4c78cb66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/custom_scheme_response.g.dart", "hash": "2db5f1dac467a7e0c241c9d066fe1288"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/safe_browsing_response_action.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.14.0/lib/firebase_core.dart", "hash": "e80d9a4901b1381733c442e0cc05a708"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs8/asn1_private_key_info.dart", "hash": "7a8930e336956f0f5da4125808add64c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "31db92b0b980a193d02b613bb9c0f819"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "530c4f96f1475cc4e4128ffedd705028"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/asn1_utils.dart", "hash": "273bbaa2c38c7b25b6392d70dc93fe66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/ecc/curves/gostr3410_2001_cryptopro_a.dart", "hash": "24b4ee2e73dff7663abc3e9e83528625"}, {"path": "/Users/<USER>/Desktop/66/web/icons/Icon-maskable-512.png", "hash": "ce3d4a1216bdefdc14a07019b7fba41e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/ajax_request.g.dart", "hash": "21e8a35c5d6ec40988b1b8fe619120f9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "05c5ca73bc4e912f53a324cfa508bbfe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/http_cookie_same_site_policy.dart", "hash": "4f0eb491ff8740b559fb1ac4557b194f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/referrer_policy.dart", "hash": "78348e5fedf0d610c50d79d82edd3891"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "1fc85ca774e46295ca83c157718278e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/cache_mode.g.dart", "hash": "d3b3f1cc4e00ec2800e609af48094020"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/profile_loader.dart", "hash": "615d6d3fd6a70784bc4e93a48e587e0d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "8dedd49e916a59b6940a666481d82e10"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "9af22b49fd7407bc0ef05667f139defd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/ext_color_buffer_float.dart", "hash": "1be3ac6ed867822ebae3ec0fe23bf389"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "837da7ede58523b5aff0ccbb40da75ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/js_prompt_response.dart", "hash": "652c75c7b3ef37cdbc667f481a877d42"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/stream/ctr.dart", "hash": "7cd0c1dd1575b6f4a192990372fd6ef6"}, {"path": "/Users/<USER>/Desktop/66/lib/design_system/kft_design_system.dart", "hash": "a5c26240b0774cff168b6d8cc9593584"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/flutter_inappwebview_platform_interface.dart", "hash": "bd2b5f997326ea308898914af2a2211b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/platform_interface/platform_interface_firebase.dart", "hash": "81402c8eea37df800d379c88bdcf6f44"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "78a0faeef5f0e801943acdca3f98393d"}, {"path": "/Users/<USER>/Desktop/66/web/manifest.json", "hash": "e62d8075ef29fde5fef1bd8b23bbe8d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/fonts/fa-brands-400.ttf", "hash": "4769f3245a24c1fa9965f113ea85ec2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/lib/src/in_app_webview/main.dart", "hash": "db78d0ad8c42445dfdadbad5b157bade"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "c9105f08cb965dfc79cdbe39f062d6c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "build/web/main.dart.js", "hash": "8e80473078df96b0d995beab956af266"}, {"path": "/Users/<USER>/Desktop/66/lib/pages/enhanced_login_page.dart", "hash": "29f20414d1b2247c989075c5eaf52baf"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "3f9362642d37e0d97860181e8a1dd598"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/materialize.dart", "hash": "7787d9ce2aed834062cd38b022824d31"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "955794ab8f9f2f33f660998c73ac222f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "e3d917994e875601c2dadaf62de546f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1/lib/src/types/web_resource_request.g.dart", "hash": "db4730ad9a6a2a6c153d099390420d3f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "920b63c794849c8a7a0f03f23314bbb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/asn1/pkcs/pkcs10/asn1_subject_public_key_info.dart", "hash": "cd3bfd17cbd8f8ff1cb9b96ab863d2df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/distinct_unique.dart", "hash": "32a430474c588e6a5dfb093a222e9f48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/src/dom/image_capture.dart", "hash": "78a1afefd2a717b10332140d9a709e6b"}]}