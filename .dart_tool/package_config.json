{"configVersion": 2, "packages": [{"name": "_flutterfire_internals", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.56", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "android_intent_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/android_intent_plus-4.0.3", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "app_settings", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_settings-5.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "archive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "asn1lib", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "awesome_notifications", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "cached_network_image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "checked_yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "chewie", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/chewie-1.11.3", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "cli_util", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "connectivity_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "connectivity_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "convert", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cross_file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "csslib", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "cupertino_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dbus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "device_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "encrypt", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "equatable", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "file_selector_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_analytics", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_analytics_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_analytics_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+13", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.14.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fixnum", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "fl_chart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.66.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_advanced_segment", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_advanced_segment-3.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_cache_manager", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_inappwebview", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_internal_annotations", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_inappwebview_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_launcher_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_riverpod", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_secure_storage", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_slidable", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-3.1.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_test", "rootUri": "file:///Users/<USER>/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///Users/<USER>/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "font_awesome_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "google_fonts", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_parser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "image_picker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "image_picker_for_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "intl", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "js", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "json_annotation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "nested", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "nm", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "octo_image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "package_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "package_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "permission_handler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.0+1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_apple", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pointycastle", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "posix", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "riverpod", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "rxdart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "screen_protector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "share_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "share_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-3.4.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "shared_preferences", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sqflite", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_common", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_darwin", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "state_notifier", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "synchronized", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "timezone", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "typed_data", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "url_launcher_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "uuid", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "video_player", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "video_player_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "video_player_avfoundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "video_player_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "video_player_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vimeo_video_player", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vimeo_video_player-1.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "wakelock_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "wakelock_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "webview_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "webview_flutter_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "webview_flutter_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "webview_flutter_wkwebview", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "win32", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "win32_registry", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "xdg_directories", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "kft", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.7"}], "generated": "2025-07-06T09:48:55.769602Z", "generator": "pub", "generatorVersion": "3.7.2", "flutterRoot": "file:///Users/<USER>/flutter", "flutterVersion": "3.29.3", "pubCache": "file:///Users/<USER>/.pub-cache"}